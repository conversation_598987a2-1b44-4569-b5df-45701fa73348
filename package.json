{"name": "s<PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.3.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "browser-image-compression": "^2.0.2", "chart.js": "^4.4.7", "cropperjs": "^1.6.2", "firebase": "^9.9.0", "framer-motion": "^11.15.0", "html-react-parser": "^5.2.2", "jodit-react": "^4.1.2", "katex": "^0.16.22", "mathlive": "^0.105.3", "react": "^18.3.1", "react-bootstrap": "^2.10.5", "react-chartjs-2": "^5.2.0", "react-cropper": "^2.3.3", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-katex": "^3.1.0", "react-loading-skeleton": "^3.5.0", "react-paginate": "^8.2.0", "react-redux": "^9.1.2", "react-router-dom": "^7.0.1", "react-select": "^5.8.3", "redux-persist": "^6.0.0", "sass": "^1.81.0", "sweetalert2": "^11.6.13"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "cssnano": "^7.0.6", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.11.0", "postcss-import": "^16.1.0", "vite": "^5.4.10"}}