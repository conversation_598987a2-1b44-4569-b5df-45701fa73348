/* Math Editor Styles */
.math-editor {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  background-color: #fff;
}

.math-input-container {
  position: relative;
}

.math-input-container math-field {
  font-family: 'Latin Modern Math', 'Computer Modern', serif;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.math-input-container math-field:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.math-preview {
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.math-preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  padding: 1rem;
}

.math-preview-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  padding: 1rem;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 0.375rem;
}

/* KaTeX overrides for better integration */
.katex {
  font-size: 1.1em;
}

.katex-display {
  margin: 0.5em 0;
}

/* Virtual keyboard positioning */
.ML__keyboard {
  z-index: 1050 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .math-input-container math-field {
    font-size: 0.9rem;
  }
  
  .katex {
    font-size: 1em;
  }
}

/* Animation for alerts */
.alert {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
