import React from "react";
import { <PERSON>, Button, ButtonGroup, Form, Badge, Accordion } from "react-bootstrap";
import { FaQuestionCircle, FaRegCircle, FaInfoCircle, FaClipboardList, FaCalendarAlt, FaGraduationCap, FaHeading, FaFileAlt } from "react-icons/fa";
import { IoFilterCircle } from "react-icons/io5";
import MathTextRenderer from "../../commonComponents/MathTextRenderer";
import CourseSubcourseBadges from "../../commonComponents/CourseSubcourseBadges";

const MasterQuestionCard = ({
  masterQuestion,
  index,
  startIndex,
  statuses,
  loading,
  handleStatusChange,
}) => {
  return (
    <Card className="mb-4 shadow-sm">
      <Card.Body>
        <div className="d-flex justify-content-start m-2">
          <ButtonGroup>
            <Button
              variant={masterQuestion?.approval_status === "rejected" ? "danger" : "outline-danger"}
              onClick={() => handleStatusChange("rejected", masterQuestion?.master_question_id, "master_question")}
              size="sm"
              disabled={loading?.[masterQuestion?.master_question_id]}
            >
              Rejected
            </Button>
            <Button
              variant={masterQuestion?.approval_status === "pending" ? "secondary" : "outline-secondary"}
              onClick={() => handleStatusChange("pending", masterQuestion?.master_question_id, "master_question")}
              size="sm"
              disabled={loading?.[masterQuestion?.master_question_id]}
            >
              Pending
            </Button>
            <Button
              variant={masterQuestion?.approval_status === "approved" ? "success" : "outline-success"}
              onClick={() => handleStatusChange("approved", masterQuestion?.master_question_id, "master_question")}
              size="sm"
              disabled={loading?.[masterQuestion?.master_question_id]}
            >
              Approved
            </Button>
          </ButtonGroup>
        </div>


        {/* Master Question Header */}
        <div className="mb-3">
          <h6 className="text-muted mb-2" style={{ fontSize: "0.85rem" }}>
            Master Question #{startIndex + index + 1} | ID: {masterQuestion?.master_question_id}
          </h6>
        </div>

        {/* Master Question Title */}
        <div className="mb-4">
          <h6 className="text-primary mb-2">
            <FaHeading className="me-2" />
            Title:
          </h6>
          <h5 className="mb-0">
            <MathTextRenderer text={masterQuestion.title} />
          </h5>
        </div>

        {/* Master Question Passage */}
        {masterQuestion.passage_content && (
          <div className="mb-4 p-3 bg-light rounded">
            <h6 className="text-primary mb-2">
              <FaFileAlt className="me-2" />
              Passage:
            </h6>
            <MathTextRenderer text={masterQuestion.passage_content} />
          </div>
        )}

        {/* Master Question Attachment */}
        {masterQuestion?.attachments && (
          <div className="master-question-image mb-3">
            <img
              src={`${import.meta.env.VITE_BASE_URL}/${masterQuestion.attachments}`}
              alt="Master Question attachment"
              className="img-fluid rounded-3 shadow-sm"
              style={{
                maxWidth: "100%",
                height: "auto",
                border: "1px solid #e9ecef"
              }}
            />
          </div>
        )}

        {/* Master Question Metadata */}
        <div className="mb-3">
          <small className="text-muted">
            <strong>Created:</strong> {new Date(masterQuestion?.created_at || Date.now()).toLocaleDateString()} |
            <strong> Approval:</strong> {masterQuestion?.approval_status}
          </small>
        </div>

        {/* Question List under Master Question */}
        <div className="mt-4">
          <h6 className="text-success mb-3">
            <FaQuestionCircle className="me-2" />
            Questions ({masterQuestion.questions?.length || 0}):
          </h6>
          {masterQuestion.questions && masterQuestion.questions.length > 0 ? (
            masterQuestion.questions.map((question) => (
              <Card key={question.question_id} className="mb-3 p-3 border-start border-primary border-3">
                {/* Question Metadata */}
                <div className="mb-3 mt-2">
                  <div className="d-flex flex-wrap gap-2 align-items-center">
                    <Badge bg="primary" size="sm">Q#{question.question_id}</Badge>
                    {question?.difficulty && (
                      <Badge bg="warning" text="dark" size="sm">
                        Difficulty: {question.difficulty}/5
                      </Badge>
                    )}
                  </div>
                  <div className="mt-2">
                    <CourseSubcourseBadges
                      subjects={question?.subject_name}
                      topics={question?.topic_name}
                      subTopics={question?.sub_topic_name}
                      maxVisibleItems={3}
                    />
                  </div>
                </div>

                {/* Course and Language Information */}
                {(question?.course || question?.language) && (
                  <div className="mb-3 mt-2">
                    <CourseSubcourseBadges
                      courses={question?.course}
                      subcourses={question?.subcourse}
                      language={question?.language}
                    />
                  </div>
                )}

                {/* Previous Year Questions */}
                {question?.previous_year_questions && question.previous_year_questions.length > 0 && (
                  <div className="mb-3 mt-2">
                    <Badge bg="danger" size="sm" className="text-wrap">
                      <FaClipboardList className="me-1" />
                      Previous Year Question
                    </Badge>
                    <div className="mt-2">
                      {question.previous_year_questions.map((pyq, idx) => (
                        <div key={idx} className="small text-muted">
                          <FaCalendarAlt className="me-1" />
                          {pyq.course?.name} - {pyq.exams?.name} ({pyq.year}, Month: {pyq.month})
                          {pyq.note && <span className="ms-2 fst-italic">Note: {pyq.note}</span>}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

              <div className="d-flex flex-column flex-sm-row justify-content-between align-items-start">
                {/* Question Content */}
                <div className="flex-grow-1">
                  <h6 className="mb-3">
                    <FaQuestionCircle className="me-2 text-primary" />
                    <MathTextRenderer text={question.content} />
                  </h6>

                  {/* Question Attachment */}
                  {question?.attachments && (
                    <div className="question-image mb-3">
                      <img
                        src={`${import.meta.env.VITE_BASE_URL}/${question.attachments}`}
                        alt="Question attachment"
                        className="img-fluid rounded-3 shadow-sm"
                        style={{
                          maxWidth: "100%",
                          height: "auto",
                          border: "1px solid #e9ecef"
                        }}
                      />
                    </div>
                  )}
                </div>

                {/* Approval Status Buttons */}
                <div className="ms-sm-auto">
                  <ButtonGroup className="mt-2 mt-sm-0">
                    <Button
                      variant={question?.approval_status === "rejected" ? "danger" : "outline-danger"}
                      onClick={() => handleStatusChange("rejected", question.question_id, "question")}
                      size="sm"
                      disabled={loading[question.question_id]}
                    >
                      Rejected
                    </Button>
                    <Button
                      variant={question?.approval_status === "pending" ? "secondary" : "outline-secondary"}
                      onClick={() => handleStatusChange("pending", question.question_id, "question")}
                      size="sm"
                      disabled={loading[question.question_id]}
                    >
                      Pending
                    </Button>
                    <Button
                      variant={question?.approval_status === "approved" ? "success" : "outline-success"}
                      onClick={() => handleStatusChange("approved", question.question_id, "question")}
                      size="sm"
                      disabled={loading[question.question_id]}
                    >
                      Approved
                    </Button>
                  </ButtonGroup>
                </div>
              </div>

              {/* Options */}
              {question.options && question.options.length > 0 ? (
                <div className="mt-3">
                  <h6 className="mb-2">Options:</h6>
                  <ol style={{ listStyleType: "decimal", padding: "0rem 1.5rem" }}>
                    {question.options.map((option) => (
                      <li
                        key={option.option_id}
                        style={{
                          color: option?.is_correct ? "#198754" : "#dc3545",
                          padding: "6px",
                          borderRadius: "4px",
                          fontSize: "0.95rem",
                          margin: "6px 0",
                          display: "flex",
                          alignItems: "flex-start",
                          gap: "6px",
                        }}
                      >
                        <FaRegCircle style={{ marginTop: "4px", flexShrink: 0 }} />
                        <div style={{ flex: 1, minWidth: 0 }}>
                          <div className="option-text">
                            <MathTextRenderer text={option?.option_text} />
                            {option?.is_correct && (
                              <Badge bg="success" size="sm" className="ms-2">Correct</Badge>
                            )}
                          </div>
                          {/* Option Attachment */}
                          {option?.attachments && (
                            <div className="option-image mt-2">
                              <img
                                src={`${import.meta.env.VITE_BASE_URL}/${option.attachments}`}
                                alt="Option attachment"
                                className="img-fluid rounded-3 shadow-sm"
                                style={{
                                  maxWidth: "200px",
                                  height: "auto",
                                  border: "1px solid #e9ecef"
                                }}
                              />
                            </div>
                          )}
                        </div>
                      </li>
                    ))}
                  </ol>
                </div>
              ) : (
                <div className="no-options text-center py-2 mt-3">
                  <p className="text-muted mb-0 small">No options available for this question.</p>
                </div>
              )}

              {/* Question Explanation */}
              {(question?.explanation || question?.explanation_attachment) && (
                <Accordion className="mt-3">
                  <Accordion.Item eventKey={`explanation-${question.question_id}`}>
                    <Accordion.Header>
                      <FaInfoCircle className="me-2" />
                      Explanation
                    </Accordion.Header>
                    <Accordion.Body>
                      {question?.explanation && (
                        <div className="explanation-text mb-3">
                          <MathTextRenderer text={question.explanation} />
                        </div>
                      )}
                      {question?.explanation_attachment && (
                        <div className="explanation-image">
                          <img
                            src={`${import.meta.env.VITE_BASE_URL}/${question.explanation_attachment}`}
                            alt="Explanation attachment"
                            className="img-fluid rounded-3 shadow-sm"
                            style={{
                              maxWidth: "100%",
                              height: "auto",
                              border: "1px solid #e9ecef"
                            }}
                          />
                        </div>
                      )}
                    </Accordion.Body>
                  </Accordion.Item>
                </Accordion>
              )}

              {/* Question Stats */}
              <div className="mt-3 pt-2 border-top">
                <small className="text-muted">
                  <strong>Created:</strong> {new Date(question?.created_at).toLocaleDateString()} |
                  <strong> Status:</strong> {question?.status} |
                  <strong> Approval:</strong> {question?.approval_status}
                  {question?.times_attempted > 0 && (
                    <>
                      {" | "}<strong>Attempted:</strong> {question.times_attempted} times |
                      <strong> Avg Score:</strong> {question.average_score?.toFixed(1)}%
                    </>
                  )}
                </small>
              </div>
              </Card>
            ))
          ) : (
            <p>No questions available under this master question.</p>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default MasterQuestionCard;
