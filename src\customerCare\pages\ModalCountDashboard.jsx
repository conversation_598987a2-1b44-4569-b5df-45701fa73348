import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getModalCounts } from "../../redux/slice/modalCountSlice";
import { Container, <PERSON>, Col, Card, Spinner, Alert } from "react-bootstrap";
import { Pie } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import { FaDatabase } from "react-icons/fa";
import NavigationBar from "../../commonComponents/NavigationBar";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

ChartJS.register(ArcElement, Tooltip, Legend);

const ModalCountDashboard = () => {
  const dispatch = useDispatch();
  const { isLoading, error } = useSelector((state) => state.modalCounts);
  const [modalCounts, setModalCounts] = useState({});

  useEffect(() => {
    const fetchData = async () => {
      const resultAction = await dispatch(getModalCounts());
      if (getModalCounts.fulfilled.match(resultAction)) {
        const data = resultAction.payload || {}; // Ensure data is always an object
        setModalCounts(data);
        if (Object.keys(data).length === 0) {
          console.warn("Modal Counts data is empty or undefined:", data);
        }
      } else {
        console.error("Failed to fetch modal counts:", resultAction.error);
      }
    };
    fetchData();
  }, [dispatch]);

  const entries = Object.entries(modalCounts).filter(([, value]) => value > 0);
  const chartData = {
    labels: entries.map(([key]) => key),
    datasets: [
      {
        label: "# of Entries",
        data: entries.map(([, value]) => value),
        backgroundColor: entries.map(
          () => `hsl(${Math.floor(Math.random() * 360)}, 70%, 70%)`
        ),
        borderWidth: 1,
      },
    ],
  };

  return (
    <>
      <NavigationBar />
      <Container className="my-4">
        <h2 className="text-center mb-4 text-success"> Analytics Dashboard</h2>

        {isLoading && (
          <Row className="mb-5">
            <Col md={6}>
              <Card className="shadow-sm">
                <Card.Body>
                  <Skeleton
                    height={250}
                    baseColor="#e6ffe6"
                    highlightColor="#c4f7c4"
                  />
                </Card.Body>
              </Card>
            </Col>
            <Col md={6}>
              <Card className="shadow-sm">
                <Card.Body>
                  <Skeleton
                    height={250}
                    baseColor="#e6ffe6"
                    highlightColor="#c4f7c4"
                  />
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}

        {error && <Alert variant="danger">{error}</Alert>}

        {!isLoading && !error && entries.length > 0 && (
          <>
            <Row className="mb-5">
              <Col md={6}>
                <Card className="shadow-sm">
                  <Card.Body>
                    <Card.Title>Distribution Chart</Card.Title>
                    <Pie data={chartData} />
                  </Card.Body>
                </Card>
              </Col>

              <Col md={6}>
                <Card className="shadow-sm">
                  <Card.Body>
                    <Card.Title>Top 5 Models</Card.Title>
                    {entries
                      .sort((a, b) => b[1] - a[1])
                      .slice(0, 5)
                      .map(([key, value]) => (
                        <div
                          key={key}
                          className="d-flex align-items-center justify-content-between mb-2"
                        >
                          <span className="text-capitalize">
                            <FaDatabase className="me-2 text-success" />
                            {key}
                          </span>
                          <span className="fw-bold">{value}</span>
                        </div>
                      ))}
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            <Row xs={1} md={3} lg={4} className="g-4">
              {entries.map(([key, value]) => (
                <Col key={key}>
                  <Card className="text-center shadow-sm h-100 border-success">
                    <Card.Body>
                      <FaDatabase size={28} className="text-success mb-2" />
                      <Card.Title className="text-capitalize">{key}</Card.Title>
                      <h5 className="fw-bold text-primary">{value}</h5>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          </>
        )}
      </Container>
    </>
  );
};

export default ModalCountDashboard;
