import React, { useRef, useState } from 'react';
import { Form, Button, ButtonGroup, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { FaBold, FaItalic, FaUnderline, FaPalette } from 'react-icons/fa';

const RichTextEditor = ({
  value = '',
  onChange,
  placeholder = 'Enter text...',
  rows = 3,
  label,
  required = false,
  className = '',
  name = '',
  disabled = false
}) => {
  const textareaRef = useRef(null);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#000000');

  // Common colors for quick selection
  const commonColors = [
    '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', 
    '#FF00FF', '#00FFFF', '#FFA500', '#800080', '#008000',
    '#800000', '#000080', '#808080', '#C0C0C0', '#808000'
  ];

  // Insert formatting tags around selected text
  const insertFormatting = (openTag, closeTag) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    
    const beforeText = value.substring(0, start);
    const afterText = value.substring(end);
    
    const newText = beforeText + openTag + selectedText + closeTag + afterText;
    
    if (onChange) {
      const event = {
        target: {
          name: name,
          value: newText
        }
      };
      onChange(event);
    }

    // Restore focus and selection
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = start + openTag.length + selectedText.length + closeTag.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  // Handle bold formatting
  const handleBold = () => {
    insertFormatting('<b>', '</b>');
  };

  // Handle italic formatting
  const handleItalic = () => {
    insertFormatting('<i>', '</i>');
  };

  // Handle underline formatting
  const handleUnderline = () => {
    insertFormatting('<u>', '</u>');
  };

  // Handle color formatting
  const handleColor = (color) => {
    insertFormatting(`<span style="color: ${color}">`, '</span>');
    setShowColorPicker(false);
  };

  // Handle custom color input
  const handleCustomColor = () => {
    handleColor(selectedColor);
  };

  // Render formatted preview
  const renderPreview = (text) => {
    if (!text) return null;
    
    // Convert HTML tags to JSX-safe format for preview
    return (
      <div 
        dangerouslySetInnerHTML={{ 
          __html: text
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/&lt;b&gt;/g, '<b>')
            .replace(/&lt;\/b&gt;/g, '</b>')
            .replace(/&lt;i&gt;/g, '<i>')
            .replace(/&lt;\/i&gt;/g, '</i>')
            .replace(/&lt;u&gt;/g, '<u>')
            .replace(/&lt;\/u&gt;/g, '</u>')
            .replace(/&lt;span style="color: ([^"]+)"&gt;/g, '<span style="color: $1">')
            .replace(/&lt;\/span&gt;/g, '</span>')
        }} 
      />
    );
  };

  return (
    <div className={className}>
      {label && <Form.Label>{label}</Form.Label>}
      
      {/* Formatting Toolbar */}
      <div className="border rounded-top p-2 bg-light">
        <div className="d-flex justify-content-between align-items-center">
          <ButtonGroup size="sm">
            <OverlayTrigger
              placement="top"
              overlay={<Tooltip>Bold</Tooltip>}
            >
              <Button
                variant="outline-secondary"
                onClick={handleBold}
                disabled={disabled}
              >
                <FaBold />
              </Button>
            </OverlayTrigger>
            
            <OverlayTrigger
              placement="top"
              overlay={<Tooltip>Italic</Tooltip>}
            >
              <Button
                variant="outline-secondary"
                onClick={handleItalic}
                disabled={disabled}
              >
                <FaItalic />
              </Button>
            </OverlayTrigger>
            
            <OverlayTrigger
              placement="top"
              overlay={<Tooltip>Underline</Tooltip>}
            >
              <Button
                variant="outline-secondary"
                onClick={handleUnderline}
                disabled={disabled}
              >
                <FaUnderline />
              </Button>
            </OverlayTrigger>
            
            <OverlayTrigger
              placement="top"
              overlay={<Tooltip>Text Color</Tooltip>}
            >
              <Button
                variant="outline-secondary"
                onClick={() => setShowColorPicker(!showColorPicker)}
                disabled={disabled}
              >
                <FaPalette />
              </Button>
            </OverlayTrigger>
          </ButtonGroup>

          <small className="text-muted">
            Select text and click formatting buttons
          </small>
        </div>

        {/* Color Picker */}
        <div className="mt-2">
          {showColorPicker && (
            <div className="border rounded p-2 bg-white">
              <div className="mb-2">
                <small className="text-muted">Quick Colors:</small>
                <div className="d-flex flex-wrap gap-1 mt-1">
                  {commonColors.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className="btn p-0 border"
                      style={{
                        width: '25px',
                        height: '25px',
                        backgroundColor: color,
                        borderRadius: '3px'
                      }}
                      onClick={() => handleColor(color)}
                      title={color}
                    />
                  ))}
                </div>
              </div>
              
              <div className="d-flex gap-2 align-items-center">
                <input
                  type="color"
                  value={selectedColor}
                  onChange={(e) => setSelectedColor(e.target.value)}
                  className="form-control form-control-color"
                  style={{ width: '40px', height: '30px' }}
                />
                <Button
                  size="sm"
                  variant="primary"
                  onClick={handleCustomColor}
                >
                  Apply
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setShowColorPicker(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Text Area */}
      <Form.Control
        ref={textareaRef}
        as="textarea"
        rows={rows}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className="font-monospace"
        style={{ fontSize: '14px' }}
      />

      {/* Preview */}
      {value && (
        <div className="border rounded-bottom p-2 bg-light">
          <small className="text-muted">Preview:</small>
          <div className="mt-1">
            {renderPreview(value)}
          </div>
        </div>
      )}
    </div>
  );
};

export default RichTextEditor;
