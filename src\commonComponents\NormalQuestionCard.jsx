import { useState } from "react";
import { <PERSON>, But<PERSON> } from "react-bootstrap";
import {
  FaQuestionCircle,
  FaRegCircle,
  FaEdit,
  FaTrashAlt,
  FaClipboardList,
  FaInfoCircle,
} from "react-icons/fa";
import QuestionRejectReasonModal from "./QuestionRejectReasonModal";
import MathTextRenderer from "./MathTextRenderer";

const NormalQuestionCard = ({ question, handleEditQuestionOption, handleDeleteQuestionOption, handleMarkAsPrevYear, handleEditQuestion, handleDeleteQuestion }) => {
  const [showReasonModal, setShowReasonModal] = useState(false);

  const handleShowReason = () => setShowReasonModal(true);
  const handleCloseReason = () => setShowReasonModal(false);

  return (
    <>
      <Card
        key={question?.question_id}
        className="shadow position-relative"
        style={{
          width: "100%",
          marginBottom: "1rem",
          backgroundColor:
            question?.approval_status === "approved"
              ? "#e6ffee"
              : question?.approval_status === "rejected"
              ? "#ffe6e6"
              : "#ffffb3",
        }}
      >
        <Card.Body>
          <div className="d-flex justify-content-between">
            <div>
              <Card.Title>
                <h6 style={{ fontSize: "0.9rem" }}>
                  Subject: {question?.subject_name} | Topic:{" "}
                  {question?.topic_name} | Sub Topic: {question?.sub_topic_name}
                </h6>
              </Card.Title>
              <Card.Text
                style={{
                  marginRight: "0.7rem",
                  textAlign: "justify",
                  fontSize: "1.1rem",
                }}
              >
                <FaQuestionCircle /> <MathTextRenderer text={question?.content} />
              </Card.Text>

              {/* Add rejection reason link */}
              {question?.approval_status === "rejected" && (
                <Button
                  variant="link"
                  className="text-danger p-0 mb-2"
                  onClick={handleShowReason}
                >
                  <FaInfoCircle className="me-1" />
                  See why it rejected
                </Button>
              )}

              {question?.attachments && (
                <Card.Img
                  variant="top"
                  src={`${import.meta.env.VITE_BASE_URL}/${
                    question?.attachments
                  }`}
                  className="img-fluid rounded-3"
                />
              )}

              {question?.options && question?.options?.length > 0 && (
                <div className="mt-2">
                  <ol
                    style={{ listStyleType: "decimal", padding: "0rem 2rem" }}
                  >
                    {question?.options.map((option) => (
                      <li
                        key={option.option_id}
                        style={{
                          color: option?.is_correct ? "#198754" : "#dc3545",
                          padding: "5px",
                          borderRadius: "4px",
                          fontSize: "1rem",
                          margin: "5px 0",
                        }}
                      >
                        <FaRegCircle /> <MathTextRenderer text={option?.option_text} />
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() =>
                            handleEditQuestionOption(option, question?.slug)
                          }
                          className="ms-2"
                        >
                          <FaEdit size={12} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() =>
                            handleDeleteQuestionOption(
                              option?.slug,
                              question?.slug
                            )
                          }
                          className="ms-2"
                        >
                          <FaTrashAlt size={12} />
                        </Button>
                      </li>
                    ))}
                  </ol>
                </div>
              )}
            </div>
            <div className="d-flex flex-column align-items-center justify-content-center">
              <Button
                variant="outline-success"
                className="action-buttons m-1"
                onClick={() => handleMarkAsPrevYear(question)}
              >
                <FaClipboardList size={15} />
              </Button>
              <Button
                variant="outline-primary"
                className="action-buttons m-1"
                onClick={() => handleEditQuestion(question)}
              >
                <FaEdit size={15} />
              </Button>
              <Button
                variant="outline-danger"
                onClick={() => handleDeleteQuestion(question?.slug)}
                className="m-1"
              >
                <FaTrashAlt size={15} />
              </Button>
            </div>
          </div>
        </Card.Body>
      </Card>

      <QuestionRejectReasonModal
        show={showReasonModal}
        onHide={handleCloseReason}
        reason={question?.reason}
        reasonDocument={question?.reason_document}
      />
    </>
  );
};

export default NormalQuestionCard;
