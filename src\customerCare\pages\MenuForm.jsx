import React, { useState, useEffect } from "react";
import { Form, Button, Card, Container, Row, Col, Modal, Dropdown } from "react-bootstrap"; // Added Modal and Dropdown
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import toast from "react-hot-toast";
import { createMenu, fetchMenus, updateMenu, deleteMenu } from "../../redux/slice/menuSlice";
import NavigationBar from "../../commonComponents/NavigationBar";
import PaginationComponent from "../../commonComponents/PaginationComponent"; // Import PaginationComponent

const MenuForm = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { access } = useSelector((state) => state?.customerCare);
  const [formData, setFormData] = useState({ title: "", body: "", image: null });
  const [menus, setMenus] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingMenu, setEditingMenu] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false); // State for modal visibility
  const [itemsPerPage, setItemsPerPage] = useState(6); // State for items per page
  const [currentPage, setCurrentPage] = useState(1); // State for current page

  const fetchMenuData = async () => {
    try {
      const response = await dispatch(fetchMenus()).unwrap();
      setMenus(response);
    } catch (error) {
      toast.error("Failed to fetch menus.");
    }
  };

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    fetchMenuData();
  }, [access, dispatch, navigate]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    if (e.target.name === "title") setSearchQuery(e.target.value); // Sync title with search bar
  };

  const handleImageChange = (e) => {
    setFormData({ ...formData, image: e.target?.files?.[0] });
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const payload = new FormData();
    payload.append("n_image", formData.image);
    payload.append("d_image", formData.image);
    payload.append("notification", JSON.stringify({ title: formData.title, body: formData.body }));
    payload.append("data", JSON.stringify({ title: formData.title, body: formData.body }));

    try {
      const resultAction = await dispatch(createMenu(payload));
      if (resultAction?.meta?.requestStatus === "fulfilled") {
        toast.success("Menu created successfully!");
        fetchMenuData();
      } else {
        toast.error("Failed to create menu.");
      }
      setFormData({ title: "", body: "", image: null });
    } catch (error) {
      toast.error("An error occurred.");
    }
  };

  const handleEdit = (menu) => {
    setEditingMenu(menu);
    setFormData({
      title: menu?.notification?.title,
      body: menu?.notification?.body,
      image: null,
    });
    setShowEditModal(true); // Show modal
  };

  const handleEditSubmit = async () => {
    const payload = new FormData();
    payload.append("n_image", formData.image);
    payload.append("d_image", formData.image);
    payload.append("notification", JSON.stringify({ title: formData.title, body: formData.body }));
    payload.append("data", JSON.stringify({ title: formData.title, body: formData.body }));

    try {
      const resultAction = await dispatch(updateMenu({ id: editingMenu.id, formData: payload }));
      if (resultAction?.meta?.requestStatus === "fulfilled") {
        toast.success("Menu updated successfully!");
        fetchMenuData();
      } else {
        toast.error("Failed to update menu.");
      }
      setEditingMenu(null);
      setShowEditModal(false); // Hide modal
    } catch (error) {
      toast.error("An error occurred.");
    }
  };

  const handleDelete = (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const resultAction = await dispatch(deleteMenu(id));
          if (resultAction?.meta?.requestStatus === "fulfilled") {
            toast.success("Menu deleted successfully!");
            fetchMenuData();
          } else {
            toast.error("Failed to delete menu.");
          }
        } catch (error) {
          toast.error("An error occurred.");
        }
      }
    });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items) => {
    setItemsPerPage(items);
    setCurrentPage(1); // Reset to first page
  };

  const filteredMenus = menus
    .filter((menu) =>
      menu?.notification?.title?.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  const totalPages = Math.ceil(menus.length / itemsPerPage);

  return (
    <>
      <NavigationBar />
      <Container className="my-5">
        <Row>
          <Col md={4}>
            <Card className="shadow">
              <Card.Body>
                <Card.Title className="text-center text-success mb-4">Add Notification Template</Card.Title>
                <Form onSubmit={handleSubmit}>
                  <Form.Group className="mb-3">
                    <Form.Label>Title</Form.Label>
                    <Form.Control
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                  <Form.Group className="mb-3">
                    <Form.Label>Body</Form.Label>
                    <Form.Control
                      type="text"
                      name="body"
                      value={formData.body}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                  <Form.Group className="mb-3">
                    <Form.Label>Upload Image</Form.Label>
                    <Form.Control
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      required
                    />
                  </Form.Group>
                  <div className="d-grid">
                    <Button variant="success" type="submit">
                      Submit
                    </Button>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Col>
          <Col md={8}>
            <Row className="mb-4">
              <Col>
                <Form.Control
                  type="text"
                  placeholder="Search menus/templates..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </Col>
              <Col md="auto">
                <Dropdown>
                  <Dropdown.Toggle variant="success">{itemsPerPage} per page</Dropdown.Toggle>
                  <Dropdown.Menu>
                    {[6, 50, 100, "All"].map((item) => (
                      <Dropdown.Item
                        key={item}
                        onClick={() => handleItemsPerPageChange(item === "All" ? menus.length : item)}
                      >
                        {item}
                      </Dropdown.Item>
                    ))}
                  </Dropdown.Menu>
                </Dropdown>
              </Col>
            </Row>
            <Row>
              {filteredMenus.map((menu) => (
                <Col md={6} key={menu.id} className="mb-4">
                  <Card className="shadow">
                    <Card.Img
                      variant="top"
                      src={menu?.notification?.image}
                      alt={menu?.notification?.title}
                    />
                    <Card.Body className="text-center">
                      <Card.Title>{menu?.notification?.title}</Card.Title>
                      <Card.Text>{menu?.notification?.body}</Card.Text>
                      <div className="d-flex justify-content-center">
                        <Button
                          variant="outline-primary"
                          className="me-2"
                          onClick={() => handleEdit(menu)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="outline-danger"
                          onClick={() => handleDelete(menu.id)}
                        >
                          Delete
                        </Button>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
            <PaginationComponent
              totalPages={totalPages}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          </Col>
        </Row>
      </Container>

      {/* Edit Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Notification Template</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Title</Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Body</Form.Label>
              <Form.Control
                type="text"
                name="body"
                value={formData.body}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Upload Image</Form.Label>
              <Form.Control
                type="file"
                accept="image/*"
                onChange={handleImageChange}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button variant="success" onClick={handleEditSubmit}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default MenuForm;
