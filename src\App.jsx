import React, { useEffect, useState } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Toast, ToastContainer } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import CustomerCareSignup from "./lanndingPages/pages/CustomerCareSignup";
import CustomerCareLogin from "./lanndingPages/pages/CustomerCareLogin";
import Blogs from './blogs/pages/Blogs';
import { Toaster, toast } from 'react-hot-toast';
import Swal from 'sweetalert2'; // Ensure you have sweetalert2 installed
import BlogDetail from './blogs/components/BlogDetail';
// import AllBlogs from './blogs/pages/AllBlogs';

// Customer Care 

import CustomerProfile from "./customerCare/pages/CustomerProfile";
import ContributorQuestions from "./customerCare/components/ContributorQuestions";
import DailyTicket from "./customerCare/components/DailyTicket";


import { initializeApp } from 'firebase/app';
import { messaging, onForegroundMessage } from './firebase';
import { getToken } from 'firebase/messaging';
import TicketDashboard from './customerCare/pages/TicketDashboard';
import PackageDashboard from './customerCare/pages/PackageDashboard';
import SignUpContentDashboard from './customerCare/pages/SignUpContentDashboard';
import StudentErrorLogs from './customerCare/pages/StudentErrorLogs';
import BannerDashboard from './customerCare/pages/BannerDashboard';
import MenuForm from './customerCare/pages/MenuForm';
import DeviceDashboard from './customerCare/pages/DeviceDashboard';
import ModalCountDashboard from './customerCare/pages/ModalCountDashboard';
import CouponDashboard from './customerCare/pages/CouponDashboard';
import GiftCardDashboard from './customerCare/pages/GiftCardDashboard';
import RewardDashboard from './customerCare/pages/RewardDashboard';
import EventsDashboard from './customerCare/pages/EventsDashboard';
import PopUpDashboard from "./customerCare/pages/PopUpDashboard";
import WalkAroundDashboard from './customerCare/pages/WalkAroundDashboard';


export default function App() {
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastTitle, setToastTitle] = useState("");
  const [toastImage, setToastImage] = useState(""); // For image URL

  const requestNotificationPermission = async () => {
    const permission = await Notification.requestPermission();
    if (permission === "granted") {
      try {
        const token = await getToken(messaging, {
          vapidKey: import.meta.env.VITE_VAPID_KEY,
        });
        if (token) {
          console.log("FCM Token:", token);
          return token;
        }
      } catch (error) {
        console.error("Error getting FCM token:", error);
      }
    } else if (permission === "denied") {
      alert(
        "You denied the permission. Please grant it to use this app efficiently"
      );
    }
  };

  useEffect(() => {
    onForegroundMessage(messaging, (payload) => {
      console.log("Foreground message received:", payload);
      if (payload.notification) {
        setToastTitle(payload.notification.title);
        setToastMessage(payload.notification.body);
        setToastImage(payload.notification.image || './notification_logo.png');
        setShowToast(true);
      }
    });
  }, []);
  

  return (
    <>
      <ToastContainer position="top-center" className="p-3">
        {showToast && (
          <Toast
            style={{
              backgroundColor: '#fff',
              color: '#333',
              borderRadius: '8px',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
              width: '100%',
            }}
          >
            <div>
              <div className="d-flex align-items-center justify-content-center">
                <div className='mr-2'>
                  <img
                    src="./notification_logo.png"
                    alt="shashtrarth"
                    className="rounded-circle"
                    style={{ marginLeft: "1em", width: '40px', height: '40px', objectFit: 'cover' }}
                  />
                </div>
                <div className='d-flex align-items-enter'>
                  <div className='mx-2'>
                    <p className='mt-2'>
                      <strong>{toastTitle}</strong>
                    </p>
                    <p className="mt-1">{toastMessage}</p>
                    <img
                      src={toastImage || './notification_logo.png'}
                      alt=""
                      style={{ marginLeft: "1em", width: '40px', height: '40px', objectFit: 'cover' }}
                    />
                  </div>
                  <div>
                    <button
                      onClick={() => setShowToast(false)}
                      className="btn text-warning"
                      style={{ margin: '10px' }}
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </Toast>
        )}
      </ToastContainer>

      <BrowserRouter>
        <Routes>
          <Route path="/" element={<CustomerCareLogin />} />
          <Route path="/customer_care_signup" element={<CustomerCareSignup />} />
          <Route path="/customer_care_login" element={<CustomerCareLogin />} />
          <Route path="/blog/:slug" element={<BlogDetail />} />
          {/* <Route path="/all_blogs" element={<AllBlogs/>} /> */}


          {/* customer care */}
          <Route path="/NormalQuestions" element={<ContributorQuestions />} />
          <Route path="/MasterQuestions" element={<ContributorQuestions />} />
          <Route path="/MasterOptions" element={<ContributorQuestions />} />
          <Route path="/Blogs" element={<ContributorQuestions />} />



          <Route path="/customer_care_dashboard" element={<CustomerProfile />} />
          <Route path="/dailyTickets" element={<DailyTicket />} />
          <Route path="/tickets" element={<TicketDashboard />} />
          <Route path="/packages" element={<PackageDashboard />} />
          <Route path="/signup-content" element={<SignUpContentDashboard />} />
          <Route path="/student-error-logs" element={<StudentErrorLogs/>} />
          <Route path="/banner" element={<BannerDashboard/>} />
          <Route path="/notification-templates" element={<MenuForm/>} />
          <Route path="/device-dashboard" element={<DeviceDashboard/>} />
          <Route path="/device-dashboard" element={<DeviceDashboard/>} />
          <Route path="/analytics-dashboard" element={<ModalCountDashboard/>} />
          <Route path="/coupon-dashboard" element={<CouponDashboard/>} />
          <Route path="/gift-card-dashboard" element={<GiftCardDashboard/>} />
          <Route path="/reward-dashboard" element={<RewardDashboard/>} />
          <Route path="/events-dashboard" element={<EventsDashboard/>} />
          <Route path="/pop-up-dashboard" element={<PopUpDashboard />} />
          <Route path="/walk-arounf-dashboard" element={<WalkAroundDashboard />} />
        </Routes>
        <Toaster/>
      </BrowserRouter>
    </>
  );
}
