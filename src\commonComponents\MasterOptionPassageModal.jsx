import React from "react";
import { Mo<PERSON>, <PERSON>, Button } from "react-bootstrap";

const MasterOptionPassageModal = ({
  show,
  handleClose,
  updatedData,
  handleInputChange,
  handleSubmit,
}) => {
  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Option</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group controlId="formTitle">
            <Form.Label>Title</Form.Label>
            <Form.Control
              type="text"
              name="title"
              value={updatedData.title}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
          <Form.Group controlId="formOptionContent" className="mt-3">
            <Form.Label>Option Content</Form.Label>
            <Form.Control
              as="textarea"
              name="option_content"
              rows={3}
              value={updatedData.option_content}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
          <Form.Group controlId="formConditions" className="mt-3">
            <Form.Label>Conditions</Form.Label>
            <Form.Control
              as="textarea"
              name="conditions"
              rows={2}
              value={updatedData.conditions}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
          <Button variant="success" type="submit" className="mt-3">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default MasterOptionPassageModal;
