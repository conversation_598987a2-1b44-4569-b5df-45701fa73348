import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import { getDevices, sendNotification, uploadImage } from "../../redux/slice/notificationSlice";
import { fetchMenus } from "../../redux/slice/menuSlice"; 
import { Table, Form, Button, Container, Row, Col, Modal } from "react-bootstrap";
import NavigationBar from "../../commonComponents/NavigationBar";

const DeviceDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { access } = useSelector((state) => state?.customerCare);

  const [filters, setFilters] = useState({
    deviceType: "",
    isRegistered: "",
    interest: "",
    registeredCourses: "",
  });
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [devices, setDevices] = useState([]);
  const [menus, setMenus] = useState([]); // State to store fetched menus
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [notificationData, setNotificationData] = useState({
    title: "",
    body: "",
    url: "https://shashtrarth.com/", // Default URL
    imageUrl: "",
    imageFile: null,
  });
  const [imageError, setImageError] = useState("");
  const [isUploading, setIsUploading] = useState(false); // State to track image upload status
  const [uploadedImageUrl, setUploadedImageUrl] = useState(""); // State to store uploaded image URL

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    const fetchMenuTemplates = async () => {
      try {
        const action = await dispatch(fetchMenus());
        if (action.meta.requestStatus === "fulfilled") {
          setMenus(action.payload);
        } else {
          toast.error("Failed to fetch templates.");
        }
      } catch (err) {
        toast.error("An error occurred while fetching templates.");
      }
    };
    fetchMenuTemplates();
  }, [access, navigate, dispatch]);

  const fetchDevices = async () => {
    setIsLoading(true);
    setError("");
    try {
      const action = await dispatch(getDevices());
      if (action.meta.requestStatus === "fulfilled") {
        setDevices(action.payload);
      } else {
        setError("Failed to fetch devices.");
      }
    } catch (err) {
      setError("An error occurred while fetching devices.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDevices();
  }, []);

  const handleFilterChange = (e) => {
    setFilters({ ...filters, [e.target.name]: e.target.value });
  };

  const toggleUserSelection = (userId) => {
    setSelectedUsers((prev) =>
      prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedUsers.length === filteredDevices.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredDevices.map((device) => device?.id));
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNotificationData({ ...notificationData, [name]: value });
    if (name === "imageUrl" && value) {
      setNotificationData({ ...notificationData, imageFile: null });
      setImageError("File upload is disabled when URL is provided.");
    } else if (name === "imageUrl" && !value) {
      setImageError("");
    }
    if (name === "imageUrl" || name === "imageFile") {
      setUploadedImageUrl(""); // Reset uploaded image URL when changing image input
    }
  };

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setIsUploading(true);
      setImageError("");
      setUploadedImageUrl("");
      try {
        const action = await dispatch(uploadImage(file));
        console.log("Upload action response:", action); // Log the entire action response for debugging
        const imageUrl = action?.payload?.image_url; // Extract image URL from the response
        if (action.meta.requestStatus === "fulfilled" && imageUrl) {
          setUploadedImageUrl(imageUrl);
          setNotificationData((prevData) => ({
            ...prevData,
            imageUrl, // Set the uploaded image URL in notificationData
            imageFile: null,
          }));
          toast.success("Image uploaded successfully!");
        } else {
          console.error("Upload failed or image_url missing in response:", action); // Log the entire action for debugging
          toast.error("Failed to upload image.");
        }
      } catch (err) {
        console.error("Unexpected error during upload:", err); // Log unexpected errors
        toast.error("An error occurred while uploading the image.");
      } finally {
        setIsUploading(false);
      }
    } else {
      setImageError("");
    }
  };

  const handleTemplateChange = (e) => {
    const selectedTemplate = menus.find((menu) => menu.id === parseInt(e.target.value));
    if (selectedTemplate) {
      setNotificationData({
        title: selectedTemplate.notification.title,
        body: selectedTemplate.notification.body,
        url: selectedTemplate.notification.url || "https://shashtrarth.com/", // Use default URL if not provided
        imageUrl: selectedTemplate.notification.image,
        imageFile: null,
      });
      setImageError(""); // Clear any existing image error
    }
  };

  const handleSendNotification = async () => {
    if (!notificationData.title.trim() && !notificationData.body.trim()) {
      toast.error("Title and Body are required.");
      return;
    }

    if (!notificationData.title.trim()) {
      toast.error("Title is required.");
      return;
    }

    if (!notificationData.body.trim()) {
      toast.error("Body is required.");
      return;
    }

    const tokens = filteredDevices
      .filter((device) => selectedUsers.includes(device?.id))
      .map((device) => device?.registration_token);

    if (!tokens.length) {
      toast.error("No devices selected.");
      return;
    }

    const payload = {
      registration_tokens: tokens,
      notification: {
        title: notificationData.title,
        body: notificationData.body,
        image: notificationData.imageFile
          ? URL.createObjectURL(notificationData.imageFile)
          : notificationData.imageUrl,
        url: notificationData.url || "https://shashtrarth.com/", // Use default URL if not provided
        icon: "/icon.png", // Add icon from the public folder
      },
      data: {
        title: notificationData.title,
        body: notificationData.body,
        image: notificationData.imageFile
          ? URL.createObjectURL(notificationData.imageFile)
          : notificationData.imageUrl,
        url: notificationData.url || "https://shashtrarth.com/", // Use default URL in data payload
        icon: "/icon.png", // Add icon in data payload
      },
    };

    try {
      await dispatch(sendNotification(payload));
      toast.success("Notification sent successfully!");
      setShowModal(false);
      setNotificationData({
        title: "",
        body: "",
        url: "https://shashtrarth.com/", // Reset to default URL
        imageUrl: "",
        imageFile: null,
      });
    } catch (err) {
      toast.error("Failed to send notification.");
    }
  };

  const filteredDevices = devices.filter((device) => {
    return (
      (!filters.deviceType || device?.device_type === filters.deviceType) &&
      (filters.isRegistered === "" || device?.is_registered === (filters.isRegistered === "true")) &&
      (!filters.interest || filters.interest.toLowerCase().split(" ").some((term) =>
        device?.interest?.some((interest) => interest?.toLowerCase().includes(term))
      )) &&
      (!filters.registeredCourses || filters.registeredCourses.toLowerCase().split(" ").some((term) =>
        device?.registered_courses?.some((course) => course?.toLowerCase().includes(term))
      ))
    );
  });

  return (
    <>
      <NavigationBar />
      <Container className="pb-5">
        <h2 className="my-3 text-center text-success">Device Dashboard</h2>
        <Row className="mb-3">
          <Col>
            <Form.Select name="deviceType" onChange={handleFilterChange}>
              <option value="">Filter by Device Type</option>
              <option value="web">Web</option>
              <option value="mobile">Mobile</option>
            </Form.Select>
          </Col>
          <Col>
            <Form.Select name="isRegistered" onChange={handleFilterChange}>
              <option value="">Filter by Registration</option>
              <option value="true">Registered</option>
              <option value="false">Not Registered</option>
            </Form.Select>
          </Col>
          <Col>
            <Form.Control
              type="text"
              placeholder="Filter by Interest"
              name="interest"
              onChange={handleFilterChange}
            />
          </Col>
          <Col>
            <Form.Control
              type="text"
              placeholder="Filter by Course"
              name="registeredCourses"
              onChange={handleFilterChange}
            />
          </Col>
        </Row>

        {isLoading ? (
          <p>Loading...</p>
        ) : error ? (
          <p className="text-danger">{error}</p>
        ) : (
          <Table striped bordered hover>
            <thead>
              <tr>
                <th>
                  <Form.Check
                    type="checkbox"
                    checked={selectedUsers.length === filteredDevices.length && filteredDevices.length > 0}
                    onChange={toggleSelectAll}
                  />
                  Select All
                </th>
                <th>ID</th>
                <th>Device Type</th>
                <th>Interest</th>
                <th>Registered Courses</th>
                <th>Registered</th>
              </tr>
            </thead>
            <tbody>
              {filteredDevices.map((device) => (
                <tr key={device?.id}>
                  <td>
                    <Form.Check
                      type="checkbox"
                      checked={selectedUsers.includes(device?.id)}
                      onChange={() => toggleUserSelection(device?.id)}
                    />
                  </td>
                  <td>{device?.id}</td>
                  <td>{device?.device_type}</td>
                  <td>{device?.interest?.join(", ")}</td>
                  <td>{device?.registered_courses?.join(", ")}</td>
                  <td>{device?.is_registered ? "Yes" : "No"}</td>
                </tr>
              ))}
            </tbody>
          </Table>
        )}
        <Button
          variant="primary"
          className="mt-3 floating-button"
          disabled={selectedUsers.length === 0}
          onClick={() => setShowModal(true)}
        >
          Send Notification ({selectedUsers.length} Selected)
        </Button>
      </Container>

      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-success">Send Notification</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Use Template</Form.Label>
              <Form.Select onChange={handleTemplateChange}>
                <option value="">Select a template</option>
                {menus.map((menu) => (
                  <option key={menu.id} value={menu.id}>
                    {menu.notification.title}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Title</Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={notificationData.title}
                onChange={handleInputChange}
                placeholder="Enter notification title"
                required
              />
              {!notificationData.title.trim() && (
                <Form.Text className="text-danger">Title is required.</Form.Text>
              )}
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Body</Form.Label>
              <Form.Control
                type="text"
                name="body"
                value={notificationData.body}
                onChange={handleInputChange}
                placeholder="Enter notification body"
                required
              />
              {!notificationData.body.trim() && (
                <Form.Text className="text-danger">Body is required.</Form.Text>
              )}
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>URL</Form.Label>
              <Form.Control
                type="text"
                name="url"
                value={notificationData.url}
                onChange={handleInputChange}
                placeholder="Enter notification URL (optional)"
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Image URL</Form.Label>
              <Form.Control
                type="text"
                name="imageUrl"
                value={notificationData.imageUrl}
                onChange={handleInputChange}
                placeholder="Enter image URL"
                disabled={!!notificationData.imageFile}
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Upload Image</Form.Label>
              <Form.Control
                type="file"
                onChange={handleFileChange}
                disabled={isUploading}
              />
              {imageError && <Form.Text className="text-danger">{imageError}</Form.Text>}
              {isUploading && <Form.Text className="text-warning">Please wait, image is uploading...</Form.Text>}
              {uploadedImageUrl && (
                <div className="mt-2">
                  <img src={uploadedImageUrl} alt="Uploaded Preview" style={{ maxWidth: "100%" }} />
                </div>
              )}
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)} disabled={isUploading}>
            Cancel
          </Button>
          <Button variant="success" onClick={handleSendNotification} disabled={isUploading}>
            Send
          </Button>
        </Modal.Footer>
      </Modal>
      <style>
        {`
          .floating-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            padding: 10px 20px;
            border-radius: 50px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
        `}
      </style>
    </>
  );
};

export default DeviceDashboard;