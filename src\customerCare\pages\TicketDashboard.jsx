import React, { useState, useEffect } from "react";
import {
  Container,
  Row,
  Col,
  InputGroup,
  FormControl,
  DropdownButton,
  Dropdown,
  Pagination,
  ButtonGroup,
  Button,
} from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getAllTickets } from "../../redux/slice/ticketSlice";
import TicketForm from "../components/TicketForm";
import TicketCard from "../components/TicketCard";
import NavigationBar from "../../commonComponents/NavigationBar";
import toast, { Toaster } from "react-hot-toast";
import ReactPaginate from "react-paginate";

const TicketDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const access = useSelector((state) => state.customerCare.access);

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
  }, [access, navigate]);

  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState(6);
  const [currentPage, setCurrentPage] = useState(0);
  const [filter, setFilter] = useState("all");

  // Fetch tickets from the API
  const fetchTickets = async () => {
    setLoading(true);
    try {
      const response = await dispatch(getAllTickets()).unwrap();
      setTickets(response);
    } catch (err) {
      setError("Failed to fetch tickets");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
  }, [dispatch]);

  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearchTerm =
      ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.student.user.email
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      ticket.student.user.first_name
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      ticket.student.user.last_name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

    const matchesFilter = filter === "all" || ticket.ticket_status === filter;

    return matchesSearchTerm && matchesFilter;
  });

  const indexOfLastTicket = (currentPage + 1) * itemsPerPage;
  const indexOfFirstTicket = indexOfLastTicket - itemsPerPage;
  const currentTickets = filteredTickets.slice(
    indexOfFirstTicket,
    indexOfLastTicket
  );

  // const handlePaginationChange = (newPage) => setCurrentPage(newPage);
  // const handleItemsPerPageChange = (num) => setItemsPerPage(num);

  // const totalPages = Math.ceil(filteredTickets.length / itemsPerPage);
  const handlePageClick = (data) => {
    setCurrentPage(data.selected);
  };

  const handleItemsPerPageChange = (num) => {
    setItemsPerPage(num);
    setCurrentPage(0); // Reset to first page
  };

  const totalPages = Math.ceil(filteredTickets.length / itemsPerPage);

  return (
    <>
      <NavigationBar />
      <Container>
        <Row>
          <Col md={5} className="mb-4">
            <TicketForm
              loading={loading}
              setSearchTerm={setSearchTerm} // Pass the setSearchTerm function to sync subject with search term
              fetchTickets={fetchTickets} // Refresh ticket list after creating a new ticket
            />
          </Col>
          <Col md={7}>
            <h3 className="mt-5 mt-sm-5 mt-3 text-center text-success">
              Available Tickets
            </h3>
            <Row className="d-flex align-items-center mb-3">
              <Col xs={6} sm={6} md={9} className="d-flex">
                <InputGroup className="w-100">
                  <FormControl
                    placeholder="Search tickets"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>
              </Col>
              <Col xs={6} sm={6} md={3} className="d-flex justify-content-end">
                <DropdownButton
                  variant="primary"
                  title={`Items per page: ${itemsPerPage}`}
                  onSelect={handleItemsPerPageChange}
                >
                  <Dropdown.Item eventKey={6}>6</Dropdown.Item>
                  <Dropdown.Item eventKey={25}>25</Dropdown.Item>
                  <Dropdown.Item eventKey={50}>50</Dropdown.Item>
                  <Dropdown.Item eventKey={100}>100</Dropdown.Item>
                </DropdownButton>
              </Col>
            </Row>
            <ButtonGroup className="mb-3">
              <Button
                variant={filter === "all" ? "primary" : "outline-primary"}
                onClick={() => setFilter("all")}
              >
                All
              </Button>
              <Button
                variant={filter === "open" ? "primary" : "outline-primary"}
                onClick={() => setFilter("open")}
              >
                Open
              </Button>
              <Button
                variant={
                  filter === "in-progress" ? "primary" : "outline-primary"
                }
                onClick={() => setFilter("in-progress")}
              >
                In Progress
              </Button>
              <Button
                variant={filter === "closed" ? "primary" : "outline-primary"}
                onClick={() => setFilter("closed")}
              >
                Closed
              </Button>
            </ButtonGroup>
            {error && <div className="alert alert-danger">{error}</div>}
            <Row className="row-cols-1 row-cols-md-2 row-cols-lg-2">
              {currentTickets.map((ticket) => (
                <Col key={ticket.id}>
                  <TicketCard ticket={ticket} onEditSuccess={fetchTickets} />
                </Col>
              ))}
            </Row>
            <ReactPaginate
              previousLabel={"Previous"}
              nextLabel={"Next"}
              breakLabel={"..."}
              breakClassName={"break-me"}
              pageCount={totalPages}
              marginPagesDisplayed={1}
              pageRangeDisplayed={3}
              onPageChange={handlePageClick}
              containerClassName={"pagination justify-content-center my-4"}
              subContainerClassName={"pages pagination"}
              activeClassName={"active"}
              pageClassName={"page-item"}
              pageLinkClassName={"page-link"}
              previousClassName={"page-item"}
              previousLinkClassName={"page-link"}
              nextClassName={"page-item"}
              nextLinkClassName={"page-link"}
              breakLinkClassName={"page-link"}
            />
          </Col>
        </Row>
        <Toaster />
      </Container>
    </>
  );
};

export default TicketDashboard;
