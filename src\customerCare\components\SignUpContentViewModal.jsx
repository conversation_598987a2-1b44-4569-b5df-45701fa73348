import React from "react";
import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";

const SignUpContentViewModal = ({ content, onClose }) => {
  return (
    <Modal show={!!content} onHide={onClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>View Sign-Up Content</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <h5>{content?.heading}</h5>
        <p><strong>Subtext 1:</strong> {content?.subtext_1}</p>
        {content?.subtext_2 && <p><strong>Subtext 2:</strong> {content.subtext_2}</p>}

        {/* Only show URLs if they exist and are not empty */}
        {Array.isArray(content?.urls) && content.urls.length > 0 && (
          <div>
            <strong>URLs:</strong>
            <ul>
              {content.urls.map((url, index) => (
                url?.trim() && <li key={index}>{url}</li>
              ))}
            </ul>
          </div>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onClose}>Close</Button>
      </Modal.Footer>
    </Modal>
  );
};

export default SignUpContentViewModal;
