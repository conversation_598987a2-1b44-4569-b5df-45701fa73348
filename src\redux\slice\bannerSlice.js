import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get auth token
const getAuthToken = (getState) => {
    const { access } = getState().customerCare; 
    return access;
};

// Base API URL
const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_BANNER_ENDPOINT}`;

// Initial state
const initialState = {
  isLoading: false,
  error: null,
};

// 🔹 Fetch all banners (GET)
export const fetchBanners = createAsyncThunk(
  "banners/fetchAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(API_URL, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching banners");
    }
  }
);

// 🔹 Create a new banner (POST)
export const createBanner = createAsyncThunk(
  "banners/create",
  async (formData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(API_URL, formData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error creating banner");
    }
  }
);

// 🔹 Update a banner (PUT)
export const updateBanner = createAsyncThunk(
  "banners/update",
  async ({ id, formData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.patch(`${API_URL}${id}/`, formData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error updating banner");
    }
  }
);

// 🔹 Delete a banner (DELETE)
export const deleteBanner = createAsyncThunk(
  "banners/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(`${API_URL}${id}/`, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return { id };
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error deleting banner");
    }
  }
);

const bannerSlice = createSlice({
  name: "banners",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Banners
      .addCase(fetchBanners.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchBanners.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(fetchBanners.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Create Banner
      .addCase(createBanner.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createBanner.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createBanner.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Update Banner
      .addCase(updateBanner.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateBanner.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updateBanner.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Delete Banner
      .addCase(deleteBanner.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteBanner.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(deleteBanner.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Actions
export const { clearError } = bannerSlice.actions;

// Reducer
export default bannerSlice.reducer;
