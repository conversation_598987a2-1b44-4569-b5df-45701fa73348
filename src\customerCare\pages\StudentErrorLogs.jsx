import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getStudentsErrors, deleteStudentError } from "../../redux/slice/studentErrorLogSlice";
import { Container, Card, Button, Modal, Form, Row, Col, Dropdown, DropdownButton } from "react-bootstrap";
import Swal from "sweetalert2";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import NavigationBar from "../../commonComponents/NavigationBar";
import PaginationComponent from "../../commonComponents/PaginationComponent"; // Import the reusable component
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";

export default function StudentErrorLogs() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { access } = useSelector((state) => state?.customerCare); // Access from store
  const { loading, error } = useSelector((state) => state?.studentErrorLogs);
  
  const [errors, setErrors] = useState([]);
  const [search, setSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [errorsPerPage, setErrorsPerPage] = useState(6); // Updated to allow dynamic selection
  const [appTypeFilter, setAppTypeFilter] = useState('all');

  const [showModal, setShowModal] = useState(false);
  const [selectedError, setSelectedError] = useState(null);

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }

    dispatch(getStudentsErrors())
      ?.unwrap()
      ?.then((data) => setErrors(data))
      ?.catch(() => {});
  }, [dispatch, access, navigate]);

  const filteredErrors = errors?.filter((error) => {
    const matchesSearch = Object.keys(error?.error_data || {})?.some((key) =>
      key?.toLowerCase()?.includes(search?.toLowerCase())
    );
    
    const matchesAppType = 
      appTypeFilter === 'all' || 
      error?.error_data?.app_type === appTypeFilter;

    return matchesSearch && matchesAppType;
  });

  const indexOfLastError = currentPage * errorsPerPage;
  const indexOfFirstError = indexOfLastError - errorsPerPage;
  const currentErrors = filteredErrors?.slice(indexOfFirstError, indexOfLastError);
  const totalPages = Math.ceil(filteredErrors?.length / errorsPerPage);

  const handleErrorsPerPageChange = (value) => {
    setErrorsPerPage(value);
    setCurrentPage(1); // Reset to the first page
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleDelete = (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result?.isConfirmed) {
        dispatch(deleteStudentError(id))
          ?.unwrap()
          ?.then(() => {
            setErrors(errors?.filter((error) => error?.id !== id));
            Swal.fire("Deleted!", "The log has been deleted.", "success");
          })
          ?.catch(() => Swal.fire("Error", "Failed to delete", "error"));
      }
    });
  };

  return (
    <>
    <NavigationBar/>
    <Container className="mt-4">
      <h3 className="text-center text-success my-3">Student Error Logs</h3>

      {/* Search Bar and Items Per Page Dropdown */}
      <div className="d-flex flex-column align-items-center mb-3">
        <div className="d-flex justify-content-center align-items-center mb-3">
          <div>
            <Form.Control
              type="text"
              placeholder="Search by error key..."
              value={search}
              onChange={(e) => setSearch(e?.target?.value)}
            />
          </div>

          <div className="ms-3">
            <DropdownButton
              id="dropdown-basic-button"
              title={`${errorsPerPage} per page`}
              variant="primary"
            >
              {[6, 12, 24, 48, 100].map((value) => (
                <Dropdown.Item
                  key={value}
                  onClick={() => handleErrorsPerPageChange(value)}
                >
                  Per page {value}
                </Dropdown.Item>
              ))}
            </DropdownButton>
          </div>
        </div>

        <div className="d-flex justify-content-center">
          <Form.Check
            inline
            type="radio"
            label="All"
            name="appType"
            checked={appTypeFilter === 'all'}
            onChange={() => setAppTypeFilter('all')}
          />
          <Form.Check
            inline
            type="radio"
            label="React Native Android"
            name="appType"
            checked={appTypeFilter === 'React Native Android'}
            onChange={() => setAppTypeFilter('React Native Android')}
          />
          <Form.Check
            inline
            type="radio"
            label="React JS Web App"
            name="appType"
            checked={appTypeFilter === 'React JS Web App'}
            onChange={() => setAppTypeFilter('React JS Web App')}
          />
        </div>
      </div>

      {/* Loading State with Skeleton */}
      {loading && (
        <Row>
          {Array.from({ length: 5 }).map((_, i) => (
            <Col md={6} lg={4} key={i}>
              <Card className="mb-3">
                <Card.Body>
                  <Skeleton height={20} width="50%" baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                  <Skeleton height={15} width="80%" baseColor="#e6ffe6" highlightColor="#c4f7c4" className="mt-2" />
                  <Skeleton height={40} width="100%" baseColor="#e6ffe6" highlightColor="#c4f7c4" className="mt-3" />
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      )}

      {/* Error Handling */}
      {error && <p className="text-danger">Failed to load errors: {error}</p>}

      {/* Display Cards */}
      {!loading && (
        <Row>
          {currentErrors?.map(({ id, error_data, created_at }) => {
            // Get the first key that isn't 'app_type'
            const errorKey = Object.keys(error_data || {})
              .filter(key => key !== 'app_type')[0];
            const errorValue = error_data?.[errorKey]?.error;

            return (
              <Col md={6} lg={4} key={id}>
                <Card className="mb-3">
                  <Card.Body>
                    <Card.Title>
                      {error_data?.app_type} || {errorKey}
                    </Card.Title>
                    <Card.Text>Created At: {new Date(created_at)?.toLocaleString()}</Card.Text>
                    <div className="d-flex justify-content-center align-items-center">
                    {/* View Button */}
                    <Button
                      variant="outline-primary"
                      className="me-2"
                      onClick={() => {
                        setSelectedError({ errorKey, errorValue });
                        setShowModal(true);
                      }}
                    >
                      View
                    </Button>

                    {/* Delete Button */}
                    <Button variant="outline-danger" onClick={() => handleDelete(id)}>
                      Delete
                    </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            );
          })}
        </Row>
      )}

      {/* Pagination */}
      <PaginationComponent
        totalPages={totalPages}
        currentPage={currentPage}
        handlePageChange={handlePageChange}
      />

      {/* Modal for Viewing Full Log */}
      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>{selectedError?.errorKey}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <pre>{JSON.stringify(selectedError?.errorValue, null, 2)}</pre>
        </Modal.Body>
      </Modal>
    </Container>
    </>
  );
}
