// src/pages/CustomerCareSignup.jsx
import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { registerCustomer } from "../../redux/slice/customerCareSlice";
import { Button, Form, Container, Row, Col, Spinner } from "react-bootstrap";
import toast, { Toaster } from "react-hot-toast";
import { useNavigate, Link } from "react-router-dom";
import { FaHeadset } from "react-icons/fa"; // Importing the customer care icon

const CustomerCareSignup = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.customerCare);

  const [formData, setFormData] = useState({
    username: "",
    email: "",
    first_name: "",
    last_name: "",
    password: "",
    contact: "",
    // Account status is directly added to the object without UI
    account_status: "active", // Set default value directly
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.username || !formData.email || !formData.password || !formData.contact) {
      toast.error("Please fill in all required fields.");
      return;
    }

    try {
      // Sending account_status directly in the payload without showing in the form
      const customerData = {
        user: {
          username: formData.username,
          email: formData.email,
          first_name: formData.first_name,
          last_name: formData.last_name,
          password: formData.password,
        },
        contact: formData.contact,
        account_status: formData.account_status, // Account status is not visible but still included in data
      };
      await dispatch(registerCustomer(customerData)).unwrap();
      toast.success("Customer care account created successfully.");
      navigate("/dashboard"); // Redirect to dashboard or another page after successful signup
    } catch (err) {
      toast.error(error?.message || "Registration failed. Please try again.");
    }
  };

  return (
    <section style={{ margin: "6rem 0rem" }}>
      <Container>
        <Row>
          <Col sm={12} md={8} lg={6} className="mx-auto">
            <div className="bg-light p-5 rounded shadow-lg">
              {/* Adding a large blue customer care icon */}
              {/* <div className="text-center mb-4">
                <FaHeadset size={70} color="#007bff" />
              </div>

              <h2 className="text-center mb-4 text-primary">Customer Care Signup</h2> */}
              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col xs={12} md={6} className="mb-3">
                    <Form.Group controlId="username">
                      <Form.Label>Username</Form.Label>
                      <Form.Control
                        type="text"
                        name="username"
                        placeholder="Enter username"
                        value={formData.username}
                        onChange={handleChange}
                      />
                    </Form.Group>

                    <Form.Group controlId="first_name">
                      <Form.Label>First Name</Form.Label>
                      <Form.Control
                        type="text"
                        name="first_name"
                        placeholder="Enter first name"
                        value={formData.first_name}
                        onChange={handleChange}
                      />
                    </Form.Group>

                    <Form.Group controlId="password">
                      <Form.Label>Password</Form.Label>
                      <Form.Control
                        type="password"
                        name="password"
                        placeholder="Enter password"
                        value={formData.password}
                        onChange={handleChange}
                      />
                    </Form.Group>

                    <Form.Group controlId="contact">
                      <Form.Label>Contact Number</Form.Label>
                      <Form.Control
                        type="text"
                        name="contact"
                        placeholder="Enter contact number"
                        value={formData.contact}
                        onChange={handleChange}
                      />
                    </Form.Group>
                  </Col>

                  <Col xs={12} md={6} className="mb-3">
                    <Form.Group controlId="email">
                      <Form.Label>Email</Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        placeholder="Enter email"
                        value={formData.email}
                        onChange={handleChange}
                      />
                    </Form.Group>

                    <Form.Group controlId="last_name">
                      <Form.Label>Last Name</Form.Label>
                      <Form.Control
                        type="text"
                        name="last_name"
                        placeholder="Enter last name"
                        value={formData.last_name}
                        onChange={handleChange}
                      />
                    </Form.Group>
                      {/* Adding a large blue customer care icon */}
              <div className="text-center mt-4 mb-4">
                <FaHeadset size={70} color="#007bff" />
              </div>

              <h4 className="text-center mb-4 text-primary">Customer Care Signup</h4>
                  </Col>
                </Row>

                <Button variant="primary" type="submit" disabled={loading} className="w-100">
                  {loading ? <Spinner animation="border" size="sm" /> : "Sign Up"}
                </Button>
                <div className="mt-3 text-center">
                   <Link to="/customer_care_login" className="text-dark text-decoration-none font-weight-bold">
                    <h6>Already have an account? Login here.</h6>
                  </Link>
                  </div>
              </Form>
            </div>
          </Col>
        </Row>
      </Container>
      <Toaster />
    </section>
  );
};

export default CustomerCareSignup;
