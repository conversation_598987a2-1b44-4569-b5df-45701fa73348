import React, { useState, useEffect } from "react";
import { Row, Col, Container, DropdownButton, Dropdown, Card, Form } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { fetchWalkArounds, createWalkAround, deleteWalkAround, updateWalkAround } from "../../redux/slice/walkAroundSlice";
import UploadWalkAroundCard from "../components/UploadWalkAroundCard";
import WalkAroundCard from "../components/WalkAroundCard";
import NavigationBar from "../../commonComponents/NavigationBar";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import Swal from "sweetalert2";

const WalkAroundDashboard = () => {
  const dispatch = useDispatch();
  const [allWalkArounds, setAllWalkArounds] = useState([]);
  const [filteredWalkArounds, setFilteredWalkArounds] = useState([]);
  const [newWalkAround, setNewWalkAround] = useState({
    title: "",
    description: "",
    image: null,
    status: "active",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState(6);
  const [currentPage, setCurrentPage] = useState(1);
  const error = useSelector((state) => state.walkarounds.error);
  const [isLoading, setIsLoading] = useState(false);

  const fetchWalkAroundsData = async () => {
    try {
      setIsLoading(true);
      const response = await dispatch(fetchWalkArounds());
      if (response?.payload) {
        const walkarounds = Array.isArray(response.payload) ? response.payload : [];
        setAllWalkArounds(walkarounds);
        filterWalkArounds(walkarounds);
      }
    } catch (error) {
      console.error("Error fetching walkarounds:", error);
      toast.error("Failed to load walkarounds");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchWalkAroundsData();
  }, [dispatch]);

  const handleAddWalkAround = async (e, resetImagePreview) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("title", newWalkAround?.title);
    formData.append("description", newWalkAround?.description);
    formData.append("status", newWalkAround?.status);
    if (newWalkAround?.image) {
      formData.append("image", newWalkAround.image);
    }
    try {
      const response = await dispatch(createWalkAround(formData));
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Walk Around added successfully!");
        setNewWalkAround({ title: "", description: "", image: null, status: "active" });
        resetImagePreview && resetImagePreview();
        fetchWalkAroundsData();
      } else {
        toast.error("Failed to add walk around");
      }
    } catch (error) {
      console.error("Error adding walk around:", error);
      toast.error(error?.message || "Failed to add walk around. Please try again!");
    }
  };

  const handleDeleteWalkAround = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });
    if (result?.isConfirmed) {
      try {
        const response = await dispatch(deleteWalkAround(id));
        if (response?.meta?.requestStatus === "fulfilled") {
          toast.success("Walk Around deleted successfully!");
          fetchWalkAroundsData();
        } else {
          toast.error("Failed to delete walk around");
        }
      } catch (error) {
        console.error("Error deleting walk around:", error);
        toast.error("Failed to delete walk around");
      }
    }
  };

  const handleUpdateWalkAround = async (id, updatedData) => {
    try {
      const formData = new FormData();
      formData.append("title", updatedData?.title);
      formData.append("description", updatedData?.description);
      formData.append("status", updatedData?.status || "active");
      if (updatedData?.image) {
        formData.append("image", updatedData.image);
      }
      const response = await dispatch(updateWalkAround({ id, formData }));
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Walk Around updated successfully!");
        fetchWalkAroundsData();
      } else {
        toast.error("Failed to update walk around");
      }
    } catch (error) {
      console.error("Error updating walk around:", error);
      toast.error("Failed to update walk around");
    }
  };

  const filterWalkArounds = (walkarounds) => {
    const filtered = Array.isArray(walkarounds)
      ? walkarounds.filter((walkaround) =>
          walkaround?.title?.toLowerCase().includes(searchTerm?.toLowerCase())
        )
      : [];
    setFilteredWalkArounds(filtered);
  };

  useEffect(() => {
    filterWalkArounds(allWalkArounds);
  }, [searchTerm, allWalkArounds]);

  const paginate = (array, pageSize, pageNumber) => {
    const offset = (pageNumber - 1) * pageSize;
    return array.slice(offset, offset + pageSize);
  };

  const handleDropdownChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const paginatedWalkArounds = paginate(filteredWalkArounds, itemsPerPage, currentPage);
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };
  const totalPages = Math.ceil(filteredWalkArounds.length / itemsPerPage);
  const indexOfLastWalkAround = currentPage * itemsPerPage;
  const indexOfFirstWalkAround = indexOfLastWalkAround - itemsPerPage;
  const currentWalkArounds = filteredWalkArounds.slice(indexOfFirstWalkAround, indexOfLastWalkAround);

  return (
    <>
      <NavigationBar />
      <Container>
        <Row className="mt-5">
          <Col md={4}>
            <UploadWalkAroundCard
              newWalkAround={newWalkAround}
              setNewWalkAround={setNewWalkAround}
              handleInputChange={(e) =>
                setNewWalkAround({ ...newWalkAround, [e.target.name]: e.target.value })
              }
              handleFileChange={(e) =>
                setNewWalkAround({ ...newWalkAround, image: e.target.files[0] })
              }
              handleAddWalkAround={handleAddWalkAround}
            />
            {error && toast.error("Something went wrong!")}
          </Col>
          <Col md={8}>
            <div className="d-flex justify-content-center mb-3">
              <Form.Control
                type="text"
                placeholder="Search by name"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ width: "60%" }}
              />
              <DropdownButton
                id="dropdown-basic-button"
                title={`Per page: ${itemsPerPage}`}
                variant="success"
                className="mx-1"
                onSelect={handleDropdownChange}
              >
                {[2, 6, 8, 16, 32, 64, 100].map((number) => (
                  <Dropdown.Item key={number} eventKey={number}>
                    {number}
                  </Dropdown.Item>
                ))}
              </DropdownButton>
            </div>
            {isLoading ? (
              <Row>
                {[...Array(itemsPerPage)].map((_, index) => (
                  <Col key={index} md={6} className="mb-3">
                    <Card className="position-relative rounded-3 shadow mb-3">
                      <Skeleton
                        height={200}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                        className="rounded-3"
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            ) : (
              <Row>
                {paginatedWalkArounds.map((walkaround) => (
                  <Col key={walkaround.id} md={6} className="mb-3">
                    <WalkAroundCard
                      walkaround={walkaround}
                      handleDeleteWalkAround={handleDeleteWalkAround}
                      handleUpdateWalkAround={handleUpdateWalkAround}
                    />
                  </Col>
                ))}
              </Row>
            )}
            <div className="d-flex justify-content-center mt-1 mb-3">
              <PaginationComponent
                totalPages={totalPages}
                currentPage={currentPage}
                handlePageChange={handlePageChange}
              />
            </div>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default WalkAroundDashboard;
