import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { access } = getState().customerCare; 
  return access;
};

// Thunks for CRUD operations

// Create a new event
export const createEvent = createAsyncThunk(
  'events/createEvent',
  async (eventData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);

      const formData = new FormData();
      Object.keys(eventData).forEach((key) => {
        if (key === 'image' && eventData[key]) {
          formData.append(key, eventData[key]); // Append image as a file
        } else if (key === 'tags' && Array.isArray(eventData[key])) {
          formData.append(key, JSON.stringify(eventData[key])); // Convert array to JSON string
        } else {
          formData.append(key, eventData[key]); // Append other data as text
        }
      });

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_EVENTS_END_POINT}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error creating event:', error);
      return rejectWithValue(error.response?.data || 'Error creating event');
    }
  }
);

// Fetch all events
export const getAllEvents = createAsyncThunk(
  'events/getAllEvents',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_EVENTS_END_POINT}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching events');
    }
  }
);

// Fetch a specific event by ID
export const getEvent = createAsyncThunk(
  'events/getEvent',
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_EVENTS_END_POINT}${id}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching event');
    }
  }
);

// Update a specific event by ID
export const updateEvent = createAsyncThunk(
  'events/updateEvent',
  async ({ id, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);

      const formData = new FormData();
      Object.keys(updatedData).forEach((key) => {
        if (key === 'image' && updatedData[key]) {
          formData.append(key, updatedData[key]); // Append image as a file
        } else if (key === 'tags' && Array.isArray(updatedData[key])) {
          formData.append(key, JSON.stringify(updatedData[key])); // Convert array to JSON string
        } else {
          formData.append(key, updatedData[key]); // Append other data as text
        }
      });

      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_EVENTS_END_POINT}${id}/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating event');
    }
  }
);

// Delete a specific event by ID
export const deleteEvent = createAsyncThunk(
  'events/deleteEvent',
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);

      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_EVENTS_END_POINT}${id}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting event');
    }
  }
);

// Slice
const eventsSlice = createSlice({
  name: 'events',
  initialState: {
    events: [],
    currentEvent: null,
    isLoading: false,
    error: null,
  },
  reducers: {
    clearCurrentEvent: (state) => {
      state.currentEvent = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle createEvent actions
      .addCase(createEvent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createEvent.fulfilled, (state, action) => {
        state.isLoading = false;
        state.events.push(action.payload);
      })
      .addCase(createEvent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle getAllEvents actions
      .addCase(getAllEvents.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAllEvents.fulfilled, (state, action) => {
        state.isLoading = false;
        state.events = action.payload;
      })
      .addCase(getAllEvents.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle getEvent actions
      .addCase(getEvent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getEvent.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentEvent = action.payload;
      })
      .addCase(getEvent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle updateEvent actions
      .addCase(updateEvent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateEvent.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.events.findIndex(event => event.id === action.payload.id);
        if (index !== -1) {
          state.events[index] = action.payload;
        }
        state.currentEvent = action.payload;
      })
      .addCase(updateEvent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle deleteEvent actions
      .addCase(deleteEvent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteEvent.fulfilled, (state, action) => {
        state.isLoading = false;
        state.events = state.events.filter(event => event.id !== action.payload);
        if (state.currentEvent && state.currentEvent.id === action.payload) {
          state.currentEvent = null;
        }
      })
      .addCase(deleteEvent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearCurrentEvent, clearError } = eventsSlice.actions;
export default eventsSlice.reducer;
