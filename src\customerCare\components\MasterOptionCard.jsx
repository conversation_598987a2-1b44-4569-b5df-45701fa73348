import React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>ton<PERSON><PERSON>, <PERSON><PERSON>, Accordion } from "react-bootstrap";
import { FaQuestionCircle, FaRegCircle, FaInfoCircle, FaClipboardList, FaCalendarAlt, FaGraduationCap, FaHeading, FaFileAlt } from "react-icons/fa";
import MathTextRenderer from "../../commonComponents/MathTextRenderer";
import CourseSubcourseBadges from "../../commonComponents/CourseSubcourseBadges";

const MasterOptionCard = ({ option, index, startIndex, statuses, loading, handleStatusChange }) => {
    return (
        <Card className="mb-4 shadow-sm">
            <Card.Body>
                <div className="d-flex justify-content-start m-2">
                    <ButtonGroup>
                        <Button
                            variant={option?.approval_status === "rejected" ? "danger" : "outline-danger"}
                            onClick={() => handleStatusChange("rejected", option?.master_option_id, "master_option")}
                            size="sm"
                            disabled={loading?.[option?.master_option_id]}
                        >
                            Rejected
                        </Button>
                        <Button
                            variant={option?.approval_status === "pending" ? "secondary" : "outline-secondary"}
                            onClick={() => handleStatusChange("pending", option?.master_option_id, "master_option")}
                            size="sm"
                            disabled={loading?.[option?.master_option_id]}
                        >
                            Pending
                        </Button>
                        <Button
                            variant={option?.approval_status ===  "approved" ? "success" : "outline-success"}
                            onClick={() => handleStatusChange("approved", option?.master_option_id, "master_option")}
                            size="sm"
                            disabled={loading?.[option?.master_option_id]}
                        >
                            Approved
                        </Button>
                    </ButtonGroup>
                </div>
                {/* Master Option Header */}
                <div className="mb-3">
                    <h6 className="text-muted mb-2" style={{ fontSize: "0.85rem" }}>
                        Master Option #{startIndex + index + 1} | ID: {option?.master_option_id}
                    </h6>
                </div>

                {/* Master Option Title */}
                <div className="mb-4">
                    <h6 className="text-primary mb-2">
                        <FaHeading className="me-2" />
                        Title:
                    </h6>
                    <h5 className="mb-0">
                        <MathTextRenderer text={option?.title} />
                    </h5>
                </div>

                {/* Master Option Content */}
                {option?.option_content && (
                    <div className="mb-4 p-3 bg-light rounded">
                        <h6 className="text-primary mb-2">
                            <FaFileAlt className="me-2" />
                            Passage:
                        </h6>
                        <MathTextRenderer text={option.option_content} />
                    </div>
                )}

                {/* Master Option Attachment */}
                {option?.attachments && (
                    <div className="master-option-image mb-3">
                        <img
                            src={`${import.meta.env.VITE_BASE_URL}/${option.attachments}`}
                            alt="Master Option attachment"
                            className="img-fluid rounded-3 shadow-sm"
                            style={{
                                maxWidth: "100%",
                                height: "auto",
                                border: "1px solid #e9ecef"
                            }}
                        />
                    </div>
                )}

                {/* Conditions (if available) */}
                {option?.conditions && (
                    <div className="mb-4 p-3 bg-warning bg-opacity-10 rounded">
                        <h6 className="text-warning-emphasis mb-2">
                            <FaInfoCircle className="me-2" />
                            Conditions:
                        </h6>
                        <MathTextRenderer text={option.conditions} />
                    </div>
                )}

                {/* Master Option Explanation */}
                {(option?.explanation || option?.explanation_attachment) && (
                    <Accordion className="mb-3">
                        <Accordion.Item eventKey={`master-explanation-${option.master_option_id}`}>
                            <Accordion.Header>
                                <FaInfoCircle className="me-2" />
                                Master Option Explanation
                            </Accordion.Header>
                            <Accordion.Body>
                                {option?.explanation && (
                                    <div className="explanation-text mb-3">
                                        <MathTextRenderer text={option.explanation} />
                                    </div>
                                )}
                                {option?.explanation_attachment && (
                                    <div className="explanation-image">
                                        <img
                                            src={`${import.meta.env.VITE_BASE_URL}/${option.explanation_attachment}`}
                                            alt="Master Option explanation attachment"
                                            className="img-fluid rounded-3 shadow-sm"
                                            style={{
                                                maxWidth: "100%",
                                                height: "auto",
                                                border: "1px solid #e9ecef"
                                            }}
                                        />
                                    </div>
                                )}
                            </Accordion.Body>
                        </Accordion.Item>
                    </Accordion>
                )}

                {/* Master Option Metadata */}
                <div className="mb-3 pt-3 border-top">
                    <small className="text-muted">
                        <strong>Created:</strong> {new Date(option?.created_at || Date.now()).toLocaleDateString()} |
                        <strong> Approval:</strong> {option?.approval_status}
                    </small>
                </div>

                {/* Related Questions (if available) */}
                <div className="mt-4">
                    <h6 className="text-success mb-3">
                        <FaQuestionCircle className="me-2" />
                        Related Questions ({option?.related_questions?.length || 0}):
                    </h6>
                    {option?.related_questions?.length > 0 ? (
                        option.related_questions.map((question) => (
                            <Card key={question?.question_id} className="mb-3 p-3 border-start border-success border-3">
                                {/* Approval Status Buttons */}
                                <div className="d-flex justify-content-end">
                                    <ButtonGroup>
                                        <Button
                                            variant={question?.approval_status === "rejected" ? "danger" : "outline-danger"}
                                            onClick={() => handleStatusChange("rejected", question?.question_id, "question")}
                                            size="sm"
                                            disabled={loading[question?.question_id]}
                                        >
                                            Rejected
                                        </Button>
                                        <Button
                                            variant={question?.approval_status === "pending" ? "secondary" : "outline-secondary"}
                                            onClick={() => handleStatusChange("pending", question?.question_id, "question")}
                                            size="sm"
                                            disabled={loading[question?.question_id]}
                                        >
                                            Pending
                                        </Button>
                                        <Button
                                            variant={question?.approval_status === "approved" ? "success" : "outline-success"}
                                            onClick={() => handleStatusChange("approved", question?.question_id, "question")}
                                            size="sm"
                                            disabled={loading[question?.question_id]}
                                        >
                                            Approved
                                        </Button>
                                    </ButtonGroup>
                                </div>

                                {/* Question Metadata */}
                                <div className="mb-3 mt-2">
                                    <div className="d-flex flex-wrap gap-2 align-items-center">
                                        <Badge bg="primary" size="sm">Q#{question?.question_id}</Badge>
                                        {question?.difficulty && (
                                            <Badge bg="warning" text="dark" size="sm">
                                                Difficulty: {question.difficulty}/5
                                            </Badge>
                                        )}
                                    </div>
                                    <div className="mt-2">
                                        <CourseSubcourseBadges
                                            subjects={question?.subject_name}
                                            topics={question?.topic_name}
                                            subTopics={question?.sub_topic_name}
                                            maxVisibleItems={3}
                                        />
                                    </div>
                                </div>

                                {/* Course and Language Information for Related Question */}
                                {(question?.course || question?.language) && (
                                    <div className="mb-3 mt-2">
                                        <CourseSubcourseBadges
                                            courses={question?.course}
                                            subcourses={question?.subcourse}
                                            language={question?.language}
                                        />
                                    </div>
                                )}

                                {/* Previous Year Questions for Related Question */}
                                {question?.previous_year_questions && question.previous_year_questions.length > 0 && (
                                    <div className="mb-3 mt-2">
                                        <Badge bg="danger" size="sm" className="text-wrap">
                                            <FaClipboardList className="me-1" />
                                            Previous Year Question
                                        </Badge>
                                        <div className="mt-2">
                                            {question.previous_year_questions.map((pyq, idx) => (
                                                <div key={idx} className="small text-muted">
                                                    <FaCalendarAlt className="me-1" />
                                                    {pyq.course?.name} - {pyq.exams?.name} ({pyq.year}, Month: {pyq.month})
                                                    {pyq.note && <span className="ms-2 fst-italic">Note: {pyq.note}</span>}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* Question Content */}
                                <h6 className="mb-3">
                                    <FaQuestionCircle className="me-2 text-primary" />
                                    <MathTextRenderer text={question?.content} />
                                </h6>

                                {/* Question Image (if available) */}
                                {question?.attachments && (
                                    <Card.Img
                                        variant="top"
                                        src={`${import.meta.env.VITE_BASE_URL}${question.attachments}`}
                                        className="img-fluid rounded-3 mb-3"
                                        alt="Question Attachment"
                                    />
                                )}

                                {/* Options under the question */}
                                {question?.options?.length > 0 ? (
                                    <div className="mt-3">
                                        <h6 className="mb-2">Options:</h6>
                                        <ol style={{ listStyleType: "decimal", padding: "0rem 1.5rem" }}>
                                            {question.options.map((opt) => (
                                                <li key={opt?.slug} style={{ marginBottom: "0.5rem" }}>
                                                    <div className="d-flex align-items-start gap-2">
                                                        <FaRegCircle
                                                            style={{
                                                                marginTop: "4px",
                                                                flexShrink: 0,
                                                                color: opt?.is_correct ? "#198754" : "#dc3545"
                                                            }}
                                                        />
                                                        <div style={{ flex: 1 }}>
                                                            <span
                                                                style={{
                                                                    color: opt?.is_correct ? "#198754" : "#dc3545",
                                                                    fontWeight: "bold",
                                                                }}
                                                            >
                                                                <MathTextRenderer text={opt?.option_text} />
                                                                {opt?.is_correct && (
                                                                    <Badge bg="success" size="sm" className="ms-2">Correct</Badge>
                                                                )}
                                                            </span>
                                                            {/* Option Attachment */}
                                                            {opt?.attachments && (
                                                                <div className="option-image mt-2">
                                                                    <img
                                                                        src={`${import.meta.env.VITE_BASE_URL}/${opt.attachments}`}
                                                                        alt="Option attachment"
                                                                        className="img-fluid rounded-3 shadow-sm"
                                                                        style={{
                                                                            maxWidth: "200px",
                                                                            height: "auto",
                                                                            border: "1px solid #e9ecef"
                                                                        }}
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </li>
                                            ))}
                                        </ol>
                                    </div>
                                ) : (
                                    <p className="text-muted">No options available for this question.</p>
                                )}

                                {/* Question Explanation */}
                                {(question?.explanation || question?.explanation_attachment) && (
                                    <Accordion className="mt-3">
                                        <Accordion.Item eventKey={`explanation-${question.question_id}`}>
                                            <Accordion.Header>
                                                <FaInfoCircle className="me-2" />
                                                Explanation
                                            </Accordion.Header>
                                            <Accordion.Body>
                                                {question?.explanation && (
                                                    <div className="explanation-text mb-3">
                                                        <MathTextRenderer text={question.explanation} />
                                                    </div>
                                                )}
                                                {question?.explanation_attachment && (
                                                    <div className="explanation-image">
                                                        <img
                                                            src={`${import.meta.env.VITE_BASE_URL}/${question.explanation_attachment}`}
                                                            alt="Explanation attachment"
                                                            className="img-fluid rounded-3 shadow-sm"
                                                            style={{
                                                                maxWidth: "100%",
                                                                height: "auto",
                                                                border: "1px solid #e9ecef"
                                                            }}
                                                        />
                                                    </div>
                                                )}
                                            </Accordion.Body>
                                        </Accordion.Item>
                                    </Accordion>
                                )}

                                {/* Question Stats */}
                                <div className="mt-3 pt-2 border-top">
                                    <small className="text-muted">
                                        <strong>Status:</strong> {question?.status} |
                                        <strong> Approval:</strong> {question?.approval_status}
                                        {question?.times_attempted > 0 && (
                                            <>
                                                {" | "}<strong>Attempted:</strong> {question.times_attempted} times
                                            </>
                                        )}
                                    </small>
                                </div>
                            </Card>
                        ))
                    ) : (
                        <p className="text-muted mt-3">No related questions available for this option.</p>
                    )}
                </div>
            </Card.Body>
        </Card>
    );
};

export default MasterOptionCard;
