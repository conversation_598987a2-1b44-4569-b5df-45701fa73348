const data = {
    "customer_care": "vipincust",
    "questions_summary": {
        "questions": {
            "total": {
                "approved": 0,
                "pending": 26,
                "rejected": 0
            }
        },
        "master_questions": {
            "total": {
                "approved": 0,
                "pending": 4,
                "rejected": 0
            }
        },
        "master_options": {
            "total": {
                "approved": 0,
                "pending": 1,
                "rejected": 0
            }
        },
        "blogs": {
            "total": {
                "approved": 6,
                "pending": 0,
                "rejected": 0
            }
        },
        "previous_questions": {
            "total": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "tickets": {
            "total": {
                "open": 1,
                "closed": 0,
                "in-progress": 0
            }
        }
    },
    "current_month_data": {
        "questions": {
            "daily": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            },
            "weekly": {
                "approved": 0,
                "pending": 19,
                "rejected": 0
            },
            "monthly": {
                "approved": 0,
                "pending": 26,
                "rejected": 0
            }
        },
        "master_questions": {
            "daily": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            },
            "weekly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            },
            "monthly": {
                "approved": 0,
                "pending": 4,
                "rejected": 0
            }
        },
        "blogs": {
            "daily": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            },
            "weekly": {
                "approved": 1,
                "pending": 0,
                "rejected": 0
            },
            "monthly": {
                "approved": 6,
                "pending": 0,
                "rejected": 0
            }
        },
        "master_options": {
            "daily": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            },
            "weekly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            },
            "monthly": {
                "approved": 0,
                "pending": 1,
                "rejected": 0
            }
        },
        "previous_questions": {
            "daily": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            },
            "weekly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            },
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "tickets": {
            "daily": {
                "open": 0,
                "closed": 0,
                "in-progress": 0
            },
            "weekly": {
                "open": 0,
                "closed": 0,
                "in-progress": 0
            },
            "monthly": {
                "open": 0,
                "closed": 0,
                "in-progress": 0
            }
        }
    },
    "previous_month_data": {
        "questions": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "master_questions": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "blogs": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "master_options": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "previous_questions": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "tickets": {
            "monthly": {
                "open": 1,
                "closed": 0,
                "in-progress": 0
            }
        }
    },
    "third_month_data": {
        "questions": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "master_questions": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "blogs": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "master_options": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "previous_questions": {
            "monthly": {
                "approved": 0,
                "pending": 0,
                "rejected": 0
            }
        },
        "tickets": {
            "monthly": {
                "open": 1,
                "closed": 0,
                "in-progress": 0
            }
        }
    }
};


export default data;