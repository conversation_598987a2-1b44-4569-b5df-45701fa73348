import React, { useState, useEffect } from "react";
import { Card, Container, Row, Col } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import { Table } from "react-bootstrap";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";
import { getCustomerDashboardData } from "../../redux/slice/customerCareSlice";

const CustomerDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(null);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchCustomerDashboardData = async () => {
      setLoading(true); // Set loading true when the data fetching starts
      try {
        const res = await dispatch(getCustomerDashboardData());
        if (res?.payload) {
          setData(res.payload); // Set the data if fetched successfully
        } else {
          console.warn("Warning: Response payload is empty or undefined.");
        }
      } catch (error) {
        console.error("Error fetching customer dashboard data:", error);
        toast.error("Error fetching customer dashboard data");
      } finally {
        setLoading(false); // Stop loading after fetching data (success or failure)
      }
    };

    fetchCustomerDashboardData();
  }, [dispatch]);

  // If data is still being fetched (loading is true), show the loader
  if (loading) {
    return (
      <Container className="my-4">
        <h3 className="text-center my-4">Loading...</h3>
        {/* You can add a spinner or skeleton loader here */}
        <Skeleton count={5} height={40} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
      </Container>
    );
  }

  // If data is fetched, display it
  return (
    <Container className="my-4">
      <h3 className="text-center my-4">Pending Tasks</h3>

      {/* Question Summary */}
      <Row>
        <h4>Question Summary</h4>
        {data?.questions ? (
          Object.keys(data.questions).map((key) => (
            <Col xs={12} md={3} className="mb-4" key={key}>
              <Card
                className="text-center h-100 shadow-sm bg-success bg-opacity-75 text-white align-items-center"
                style={{ height: "150px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>{key.replace("_", " ").toUpperCase()}</Card.Title>
                  <Card.Text className="display-6 fw-bold">
                    {data.questions[key].total_questions}
                  </Card.Text>
                  <Card.Subtitle>Pending</Card.Subtitle>
                </Card.Body>
              </Card>
            </Col>
          ))
        ) : (
          <div>No data available for Question Summary</div>
        )}
      </Row>

      {/* Tickets Summary */}
      <Row>
        <h4>Tickets Summary</h4>
        {data?.tickets ? (
          Object.keys(data.tickets).map((key) => (
            <Col xs={12} md={3} className="mb-4" key={key}>
              <Card
                className="text-center h-100 shadow-sm bg-primary bg-opacity-75 text-white align-items-center"
                style={{ height: "150px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>{key.replace("_", " ").toUpperCase()}</Card.Title>
                  <Card.Text className="display-6 fw-bold">
                    {data.tickets[key].total_tickets}
                  </Card.Text>
                  <Card.Subtitle>Pending</Card.Subtitle>
                </Card.Body>
              </Card>
            </Col>
          ))
        ) : (
          <div>No data available for Tickets</div>
        )}
      </Row>

      {/* Master Questions */}
      <Row>
        <h4>Master Questions</h4>
        {data?.master_questions ? (
          Object.keys(data.master_questions).map((key) => (
            <Col xs={12} md={3} className="mb-4" key={key}>
              <Card
                className="text-center h-100 shadow-sm bg-info bg-opacity-75 text-white align-items-center"
                style={{ height: "150px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>{key.replace("_", " ").toUpperCase()}</Card.Title>
                  <Card.Text className="display-6 fw-bold">
                    {data.master_questions[key].total_questions}
                  </Card.Text>
                  <Card.Subtitle>Pending</Card.Subtitle>
                </Card.Body>
              </Card>
            </Col>
          ))
        ) : (
          <div>No data available for Master Questions</div>
        )}
      </Row>

      {/* Master Options */}
      <Row>
        <h4>Master Options</h4>
        {data?.master_options ? (
          Object.keys(data.master_options).map((key) => (
            <Col xs={12} md={3} className="mb-4" key={key}>
              <Card
                className="text-center h-100 shadow-sm bg-warning bg-opacity-75 text-white align-items-center"
                style={{ height: "150px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>{key.replace("_", " ").toUpperCase()}</Card.Title>
                  <Card.Text className="display-6 fw-bold">
                    {data.master_options[key].total_master_options}
                  </Card.Text>
                  <Card.Subtitle>Pending</Card.Subtitle>
                </Card.Body>
              </Card>
            </Col>
          ))
        ) : (
          <div>No data available for Master Options</div>
        )}
      </Row>

      {/* Blogs Summary */}
      <Row>
        <h4>Blogs Summary</h4>
        {data?.blogs ? (
          Object.keys(data.blogs).map((key) => (
            <Col xs={12} md={3} className="mb-4" key={key}>
              <Card
                className="text-center h-100 shadow-sm bg-danger bg-opacity-75 text-white align-items-center"
                style={{ height: "150px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>{key.replace("_", " ").toUpperCase()}</Card.Title>
                  <Card.Text className="display-6 fw-bold">
                    {data.blogs[key].total_blogs}
                  </Card.Text>
                  <Card.Subtitle>Pending</Card.Subtitle>
                </Card.Body>
              </Card>
            </Col>
          ))
        ) : (
          <div>No data available for Blogs</div>
        )}
      </Row>
    </Container>
  );
};

export default CustomerDashboard;
