import React, { useState, useEffect } from "react";
import { Form, But<PERSON>, Card, Container, Row, Col, Modal, Dropdown } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import {
  createCoupon,
  fetchCoupons,
  updateCoupon,
  deleteCoupon,
} from "../../redux/slice/couponSlice";
import NavigationBar from "../../commonComponents/NavigationBar";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import { FaTags } from 'react-icons/fa';

const CouponDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { access } = useSelector((state) => state.customerCare);

  const [formData, setFormData] = useState({
    discount: "",
    discount_type: "amount",
    count: "",
    usage_limit: "",
    expiry_date: "",
    codes: "",
  });

  const [coupons, setCoupons] = useState([]);
  const [editingCoupon, setEditingCoupon] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showEditModal, setShowEditModal] = useState(false);
  const [loading, setLoading] = useState(false); // For submission button
  const [isFetching, setIsFetching] = useState(true); // For skeleton loading

  const [itemsPerPage, setItemsPerPage] = useState(6); // Default items per page
  const [currentPage, setCurrentPage] = useState(1); // Current page
  const [totalPages, setTotalPages] = useState(1); // Total pages

  const fetchCouponData = async () => {
    try {
      const response = await dispatch(fetchCoupons()).unwrap();
      setCoupons(response);
      console.log("SET COUPONS", response);
    } catch (err) {
      toast.error("Failed to fetch coupons.");
    }
  };

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    const fetchData = async () => {
      setIsFetching(true);
      await fetchCouponData();
      setIsFetching(false);
    };
    fetchData();
  }, [access, dispatch, navigate]);

  const filteredCoupons = coupons.filter((c) =>
    c.code?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    setTotalPages(Math.ceil(filteredCoupons.length / itemsPerPage));
  }, [filteredCoupons, itemsPerPage]);

  const handleChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleCountChange = (e) => {
    const count = parseInt(e.target.value) || 0;
    setFormData((prev) => ({
      ...prev,
      count: count,
      codes: Array(count).fill(""), // Create empty fields for codes
    }));
  };

  const handleCodeChange = (index, value) => {
    if (value.length > 10) {
      toast.error("Coupon code cannot exceed 10 characters.");
      value = value.slice(0, 10); // Truncate to 10 characters
    }
    setFormData((prev) => {
      const updatedCodes = [...prev.codes];
      updatedCodes[index] = value;
      return { ...prev, codes: updatedCodes };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true); // Show "Creating..." on button
    const payload = {
      discount: parseFloat(formData.discount),
      discount_type: formData.discount_type,
      count: parseInt(formData.count),
      usage_limit: parseInt(formData.usage_limit),
      expiry_date: formData.expiry_date,
    };

    if (formData.codes.some((code) => code.trim())) {
      payload.codes = formData.codes.filter((code) => code.trim());
    }

    try {
      const result = await dispatch(createCoupon(payload));
      if (result.meta.requestStatus === "fulfilled") {
        toast.success("Coupon created successfully!");
        fetchCouponData();
      } else {
        toast.error("Failed to create coupon.");
      }

      setFormData({
        discount: "",
        discount_type: "amount",
        count: "",
        usage_limit: "",
        expiry_date: "",
        codes: "",
      });
    } catch (err) {
      toast.error("An error occurred.");
    } finally {
      setLoading(false); // Reset button state
    }
  };

  const handleEdit = (coupon) => {
    setEditingCoupon(coupon);
    setFormData({
      discount: coupon.discount,
      discount_type: coupon.discount_type,
      usage_limit: coupon.usage_limit,
      expiry_date: coupon.expiry_date.split("T")[0], // Extract date part
      codes: coupon.code || "", // Adjust for single code
    });
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setEditingCoupon(null);
    setFormData({
      discount: "",
      discount_type: "amount",
      usage_limit: "",
      expiry_date: "",
      codes: "",
    });
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    const payload = {
      discount: parseFloat(formData.discount),
      discount_type: formData.discount_type,
      usage_limit: parseInt(formData.usage_limit),
      expiry_date: formData.expiry_date,
    };

    if (formData.codes.trim()) {
      payload.codes = formData.codes.split(",").map((code) => code.trim());
    }

    try {
      const result = await dispatch(updateCoupon({ id: editingCoupon.id, formData: payload }));
      if (result.meta.requestStatus === "fulfilled") {
        toast.success("Coupon updated successfully!");
        fetchCouponData();
        handleCloseEditModal();
      } else {
        toast.error("Failed to update coupon.");
      }
    } catch (err) {
      toast.error("An error occurred.");
    }
  };

  const handleDelete = (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "This will permanently delete the coupon!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const resultAction = await dispatch(deleteCoupon(id));
          if (resultAction.meta.requestStatus === "fulfilled") {
            toast.success("Coupon deleted successfully!");
            fetchCouponData();
          } else {
            toast.error("Failed to delete coupon.");
          }
        } catch {
          toast.error("An error occurred.");
        }
      }
    });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page
  };

  const paginatedCoupons = filteredCoupons.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <>
      <NavigationBar />
      <Container className="my-5">
        <Row>
          <Col md={4}>
            <Card className="shadow">
              <Card.Body>
                <Card.Title className="text-center text-success mb-4">
                  Add Coupon
                </Card.Title>
                <Form onSubmit={handleSubmit}>
                  <Form.Group className="mb-3">
                    <Form.Label>Discount</Form.Label>
                    <Form.Control
                      type="number"
                      name="discount"
                      value={formData.discount}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Discount Type</Form.Label>
                    <Form.Select
                      name="discount_type"
                      value={formData.discount_type}
                      onChange={handleChange}
                    >
                      <option value="amount">Amount</option>
                      <option value="percentage">Percentage</option>
                    </Form.Select>
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Coupon Count</Form.Label>
                    <Form.Control
                      type="number"
                      name="count"
                      value={formData.count}
                      onChange={handleCountChange}
                      required
                    />
                  </Form.Group>

                  {Array.from({ length: formData.count || 0 }).map((_, index) => (
                    <Form.Group className="mb-3" key={index}>
                      <Form.Label>Coupon Code {index + 1}</Form.Label>
                      <Form.Control
                        type="text"
                        value={formData.codes[index] || ""}
                        onChange={(e) => handleCodeChange(index, e.target.value)}
                        placeholder="Leave empty to auto-generate"
                      />
                    </Form.Group>
                  ))}

                  <Form.Group className="mb-3">
                    <Form.Label>Usage Limit</Form.Label>
                    <Form.Control
                      type="number"
                      name="usage_limit"
                      value={formData.usage_limit}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Expiry Date</Form.Label>
                    <Form.Control
                      type="date"
                      name="expiry_date"
                      value={formData.expiry_date}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>

                  <div className="d-grid">
                    <Button variant="success" type="submit" disabled={loading}>
                      {loading ? "Creating..." : "Create"}
                    </Button>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Col>

          <Col md={8}>
            <Row className="my-4 align-items-center">
              <Col xs={8} sm={9}>
                <Form.Control
                  type="text"
                  placeholder="Search by coupon codes..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Col>
              <Col xs={4} sm={3} className="text-end">
                <Dropdown>
                  <Dropdown.Toggle variant="success" id="itemsPerPageDropdown" className="w-100">
                    {itemsPerPage === filteredCoupons.length ? "5 per page" : `${itemsPerPage} per page`}
                  </Dropdown.Toggle>
                  <Dropdown.Menu>
                    <Dropdown.Item onClick={() => handleItemsPerPageChange(6)}>
                      6 per page
                    </Dropdown.Item>
                    <Dropdown.Item onClick={() => handleItemsPerPageChange(50)}>
                      50 per page
                    </Dropdown.Item>
                    <Dropdown.Item onClick={() => handleItemsPerPageChange(100)}>
                      100 per page
                    </Dropdown.Item>
                    <Dropdown.Item onClick={() => handleItemsPerPageChange(filteredCoupons.length)}>
                      All
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              </Col>
            </Row>
            <Row>
              {isFetching
                ? Array.from({ length: 4 }).map((_, index) => (
                    <Col md={6} lg={4} key={index} className="mb-4">
                      <Skeleton
                        height={150}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    </Col>
                  ))
                : paginatedCoupons.map((coupon) => (
                    <Col md={6} lg={4} key={coupon.id} className="mb-4">
                      <Card className="shadow">
                        <Card.Body>
                          <Card.Title className="text-center">
                          <FaTags className="text-success me-1"/>
                            {coupon.discount_type === "percentage"
                              ? `${coupon.discount}% OFF`
                              : `₹${coupon.discount} OFF`}
                          </Card.Title>
                          <Card.Text>
                            <strong>Code:</strong> {coupon.code || "Auto-generated"}
                          </Card.Text>
                          <Card.Text>                        
                            <strong>Usage Limit:</strong> {coupon.usage_limit}
                            <br />
                            <strong>Expires:</strong> {coupon.expiry_date.split("T")[0]}
                            <br />
                            <strong>Status:</strong> {coupon.is_active ? "Active" : "Inactive"}
                          </Card.Text>
                          <div className="d-flex justify-content-center">
                            <Button
                              variant="outline-primary"
                              className="me-2"
                              onClick={() => handleEdit(coupon)}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="outline-danger"
                              onClick={() => handleDelete(coupon.id)}
                            >
                              Delete
                            </Button>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  ))}
            </Row>
            <PaginationComponent
              totalPages={totalPages}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          </Col>
        </Row>
      </Container>

      <Modal show={showEditModal} onHide={handleCloseEditModal} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-primary">Edit Coupon</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Discount</Form.Label>
              <Form.Control
                type="number"
                name="discount"
                value={formData.discount}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Discount Type</Form.Label>
              <Form.Select
                name="discount_type"
                value={formData.discount_type}
                onChange={handleChange}
              >
                <option value="amount">Amount</option>
                <option value="percentage">Percentage</option>
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Usage Limit</Form.Label>
              <Form.Control
                type="number"
                name="usage_limit"
                value={formData.usage_limit}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Expiry Date</Form.Label>
              <Form.Control
                type="date"
                name="expiry_date"
                value={formData.expiry_date}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Coupon Code </Form.Label>
              <Form.Control
                type="text"
                name="codes"
                value={formData.codes}
                onChange={(e) => {
                  if (e.target.value.length > 10) {
                    toast.error("Coupon code cannot exceed 10 characters.");
                    e.target.value = e.target.value.slice(0, 10); // Truncate to 10 characters
                  }
                  handleChange(e);
                }}
                placeholder="Leave empty to auto-generate"
              />
            </Form.Group>
            <div className="d-grid">
              <Button variant="primary" type="submit">
                Update
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default CouponDashboard;
