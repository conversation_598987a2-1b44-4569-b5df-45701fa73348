import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const EditBannerModal = ({ show, handleClose, banner, handleUpdate }) => {
  const [updatedBanner, setUpdatedBanner] = useState({
    banner_name: "",
    banner_image: null,
  });

  useEffect(() => {
    if (banner) {
      setUpdatedBanner({
        banner_name: banner.banner_name,
        banner_image: null, // Image update is optional
      });
    }
  }, [banner]);

  const handleInputChange = (e) => {
    setUpdatedBanner({ ...updatedBanner, banner_name: e.target.value });
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setUpdatedBanner({ ...updatedBanner, banner_image: file });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    handleUpdate(banner.id, updatedBanner);
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Banner</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Banner Name</Form.Label>
            <Form.Control
              type="text"
              value={updatedBanner.banner_name}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Banner Image</Form.Label>
            <Form.Control type="file" onChange={handleFileChange} />
          </Form.Group>
          <Button variant="primary" type="submit">
            Update
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default EditBannerModal;
