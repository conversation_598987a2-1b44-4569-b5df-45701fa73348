import React, { useState } from "react";
import { Form, Button, Row, Col } from "react-bootstrap";

const SignUpContentForm = ({ onSubmit, setSearchTerm }) => {
  const [formData, setFormData] = useState({
    heading: "",
    subtext_1: "",
    subtext_2: "",
    urls: [""],
  });

  const [errors, setErrors] = useState({});

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Update search bar when heading is typed
    if (name === "heading") {
      setSearchTerm(value);
    }
  };

  // Handle URL change
  const handleUrlChange = (index, value) => {
    const updatedUrls = [...formData.urls];
    updatedUrls[index] = value;
    setFormData((prev) => ({ ...prev, urls: updatedUrls }));
  };

  // Add new URL input field
  const addUrlField = () => {
    setFormData((prev) => ({ ...prev, urls: [...prev.urls, ""] }));
  };

  // Remove a URL input field
  const removeUrlField = (index) => {
    const updatedUrls = formData.urls.filter((_, i) => i !== index);
    setFormData((prev) => ({ ...prev, urls: updatedUrls }));
  };

  // Form validation
  const validateForm = () => {
    let newErrors = {};
    if (!formData.heading.trim()) newErrors.heading = "Heading is required";
    if (!formData.subtext_1.trim()) newErrors.subtext_1 = "Subtext 1 is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    onSubmit(formData);
    setFormData({ heading: "", subtext_1: "", subtext_2: "", urls: [""] });
    setSearchTerm("");
  };

  return (
    <Form onSubmit={handleSubmit} className="p-3 border rounded shadow-sm bg-white">
      <h4 className="mb-3 text-success text-center">Create Sign-Up Content</h4>

      <Form.Group className="mb-3">
        <Form.Label>Heading *</Form.Label>
        <Form.Control
          type="text"
          name="heading"
          value={formData.heading}
          onChange={handleChange}
          isInvalid={!!errors.heading}
        />
        <Form.Control.Feedback type="invalid">{errors.heading}</Form.Control.Feedback>
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Subtext 1 *</Form.Label>
        <Form.Control
          type="text"
          name="subtext_1"
          value={formData.subtext_1}
          onChange={handleChange}
          isInvalid={!!errors.subtext_1}
        />
        <Form.Control.Feedback type="invalid">{errors.subtext_1}</Form.Control.Feedback>
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Subtext 2</Form.Label>
        <Form.Control
          type="text"
          name="subtext_2"
          value={formData.subtext_2}
          onChange={handleChange}
        />
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>URLs</Form.Label>
        {formData.urls.map((url, index) => (
          <Row key={index} className="mb-2">
            <Col xs={9}>
              <Form.Control
                type="text"
                value={url}
                onChange={(e) => handleUrlChange(index, e.target.value)}
              />
            </Col>
            <Col xs={3}>
              {index > 0 && (
                <Button variant="danger" onClick={() => removeUrlField(index)}>
                  -
                </Button>
              )}
            </Col>
          </Row>
        ))}
        <Button variant="primary" onClick={addUrlField}>
          + Add URL
        </Button>
      </Form.Group>

      <Button variant="success" type="submit" className="w-100">
        Submit
      </Button>
    </Form>
  );
};

export default SignUpContentForm;
