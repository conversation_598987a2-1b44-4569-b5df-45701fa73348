import React, { useEffect, useRef, useState } from 'react';
import { MathfieldElement } from 'mathlive';

const MathInput = ({ 
  value = '', 
  onChange, 
  placeholder = 'Enter mathematical expression...', 
  className = '',
  disabled = false,
  showVirtualKeyboard = true 
}) => {
  const mathfieldRef = useRef(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Ensure MathfieldElement is defined
    if (!customElements.get('math-field')) {
      customElements.define('math-field', MathfieldElement);
    }

    const mathfield = mathfieldRef.current;
    if (!mathfield) return;

    // Configure the mathfield
    mathfield.setOptions({
      virtualKeyboardMode: showVirtualKeyboard ? 'manual' : 'off',
      virtualKeyboards: 'all',
      smartFence: true,
      smartSuperscript: true,
      scriptDepth: [Infinity, Infinity],
      removeExtraneousParentheses: true,
    });

    // Set initial value
    if (value && mathfield.getValue() !== value) {
      mathfield.setValue(value);
    }

    // Handle input changes
    const handleInput = () => {
      const latex = mathfield.getValue();
      if (onChange) {
        onChange(latex);
      }
    };

    // Handle focus events
    const handleFocus = () => {
      if (showVirtualKeyboard) {
        mathfield.executeCommand('showVirtualKeyboard');
      }
    };

    const handleBlur = () => {
      if (showVirtualKeyboard) {
        mathfield.executeCommand('hideVirtualKeyboard');
      }
    };

    // Add event listeners
    mathfield.addEventListener('input', handleInput);
    mathfield.addEventListener('focus', handleFocus);
    mathfield.addEventListener('blur', handleBlur);

    setIsReady(true);

    // Cleanup
    return () => {
      if (mathfield) {
        mathfield.removeEventListener('input', handleInput);
        mathfield.removeEventListener('focus', handleFocus);
        mathfield.removeEventListener('blur', handleBlur);
      }
    };
  }, [showVirtualKeyboard, onChange]);

  // Update value when prop changes
  useEffect(() => {
    const mathfield = mathfieldRef.current;
    if (mathfield && isReady && value !== mathfield.getValue()) {
      mathfield.setValue(value);
    }
  }, [value, isReady]);

  return (
    <div className={`math-input-container ${className}`}>
      <math-field
        ref={mathfieldRef}
        style={{
          width: '100%',
          minHeight: '50px',
          border: '1px solid #ced4da',
          borderRadius: '0.375rem',
          padding: '0.375rem 0.75rem',
          fontSize: '1rem',
          lineHeight: '1.5',
          backgroundColor: disabled ? '#e9ecef' : '#fff',
          opacity: disabled ? 0.65 : 1,
          pointerEvents: disabled ? 'none' : 'auto',
        }}
        placeholder={placeholder}
      />
    </div>
  );
};

export default MathInput;
