import React from 'react';
import { InlineMath, BlockMath } from 'react-katex';

/**
 * Component to render text with embedded LaTeX math expressions
 * Supports both inline math ($...$) and display math ($$...$$)
 */
const MathTextRenderer = ({ 
  text = '', 
  className = '',
  fallbackToPlainText = true 
}) => {
  if (!text || typeof text !== 'string') {
    return <span className={className}>{text}</span>;
  }

  try {
    // Parse text for math expressions
    const parts = parseTextWithMath(text);

    // Debug logging for problematic text
    if (text.includes('∫') || text.includes('​')) {
      console.log('MathTextRenderer Debug:', {
        originalText: text,
        parsedParts: parts,
        containsIntegral: text.includes('∫'),
        containsZeroWidth: text.includes('​')
      });
    }

    if (parts.length === 1 && parts[0].type === 'text') {
      // No math found, return text with HTML formatting
      return (
        <span
          className={className}
          dangerouslySetInnerHTML={{ __html: text }}
        />
      );
    }

    return (
      <span className={className}>
        {parts.map((part, index) => {
          switch (part.type) {
            case 'text':
              return (
                <span
                  key={index}
                  dangerouslySetInnerHTML={{ __html: part.content }}
                />
              );
            case 'inline-math':
              try {
                return (
                  <InlineMath
                    key={index}
                    math={part.content}
                    errorColor="#dc3545"
                  />
                );
              } catch (mathError) {
                console.warn('Error rendering inline math:', part.content, mathError);
                return (
                  <span key={index} className="text-danger" title={`Math error: ${mathError.message}`}>
                    ${part.content}$
                  </span>
                );
              }
            case 'display-math':
              try {
                return (
                  <BlockMath
                    key={index}
                    math={part.content}
                    errorColor="#dc3545"
                  />
                );
              } catch (mathError) {
                console.warn('Error rendering display math:', part.content, mathError);
                return (
                  <div key={index} className="text-danger" title={`Math error: ${mathError.message}`}>
                    $${part.content}$$
                  </div>
                );
              }
            default:
              return (
                <span
                  key={index}
                  dangerouslySetInnerHTML={{ __html: part.content }}
                />
              );
          }
        })}
      </span>
    );
  } catch (error) {
    console.warn('Error rendering math in text:', error);
    if (fallbackToPlainText) {
      return (
        <span
          className={className}
          dangerouslySetInnerHTML={{ __html: text }}
        />
      );
    }
    return (
      <span className={`${className} text-danger`}>
        <small>Error rendering math content</small>
      </span>
    );
  }
};

/**
 * Parse text containing math expressions delimited by $ and $$
 * Returns array of objects with type and content
 */
function parseTextWithMath(text) {
  const parts = [];
  let currentIndex = 0;

  // Clean the text first to handle common issues
  const cleanedText = cleanTextForMath(text);

  // Regular expression to match $...$ and $$...$$ patterns
  const mathRegex = /(\$\$[\s\S]*?\$\$|\$[^$\n]*?\$)/g;
  let match;

  while ((match = mathRegex.exec(cleanedText)) !== null) {
    // Add text before the math expression
    if (match.index > currentIndex) {
      const textContent = cleanedText.slice(currentIndex, match.index);
      if (textContent) {
        parts.push({
          type: 'text',
          content: textContent
        });
      }
    }

    // Add the math expression
    const mathContent = match[1];
    if (mathContent.startsWith('$$') && mathContent.endsWith('$$')) {
      // Display math
      const latex = mathContent.slice(2, -2).trim();
      if (latex) {
        parts.push({
          type: 'display-math',
          content: latex
        });
      }
    } else if (mathContent.startsWith('$') && mathContent.endsWith('$')) {
      // Inline math
      const latex = mathContent.slice(1, -1).trim();
      if (latex) {
        parts.push({
          type: 'inline-math',
          content: latex
        });
      }
    }

    currentIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (currentIndex < cleanedText.length) {
    const remainingText = cleanedText.slice(currentIndex);
    if (remainingText) {
      parts.push({
        type: 'text',
        content: remainingText
      });
    }
  }

  // If no parts were found, return the original text as a single text part
  if (parts.length === 0) {
    parts.push({
      type: 'text',
      content: cleanedText
    });
  }

  return parts;
}

/**
 * Clean text to handle common formatting issues that can break math parsing
 */
function cleanTextForMath(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text
    // Remove zero-width spaces and other invisible characters
    .replace(/[\u200B-\u200D\uFEFF]/g, '')

    // Fix common copy-paste issues from formatted text
    // Handle cases where superscripts become separate numbers
    .replace(/(\w+)\s*\n\s*(\d+)\s*\n/g, '$1^{$2} ')  // Handle line breaks in superscripts
    .replace(/(\w+)\s*\u2009\s*(\d+)/g, '$1^{$2}')  // Thin space before superscript
    .replace(/(\w+)\s*\u00A0\s*(\d+)/g, '$1^{$2}')  // Non-breaking space before superscript

    // Handle integral bounds that get separated by line breaks
    .replace(/∫\s*\n\s*(\d+)\s*\n\s*(\w+)/g, '∫_{$1}^{$2}')
    .replace(/∫\s+(\d+)\s+(\w+)\s*​/g, '∫_{$1}^{$2}')  // Zero-width space after bounds

    // Handle fraction notation that gets broken
    .replace(/(\d+)\s*\n\s*\/\s*\n\s*(\d+)/g, '\\frac{$1}{$2}')
    .replace(/(\d+)\s*\/\s*(\d+)/g, '\\frac{$1}{$2}')

    // Handle square root notation
    .replace(/√\s*\(([^)]+)\)/g, '\\sqrt{$1}')
    .replace(/√(\w+)/g, '\\sqrt{$1}')

    // Handle common mathematical symbols
    .replace(/±/g, '\\pm')
    .replace(/∞/g, '\\infty')
    .replace(/π/g, '\\pi')
    .replace(/α/g, '\\alpha')
    .replace(/β/g, '\\beta')
    .replace(/γ/g, '\\gamma')
    .replace(/δ/g, '\\delta')
    .replace(/θ/g, '\\theta')
    .replace(/λ/g, '\\lambda')
    .replace(/μ/g, '\\mu')
    .replace(/σ/g, '\\sigma')
    .replace(/φ/g, '\\phi')
    .replace(/ω/g, '\\omega')

    // Handle degree symbol
    .replace(/°/g, '^\\circ')

    // Clean up multiple spaces
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Utility function to check if text contains math expressions
 */
export function containsMath(text) {
  if (!text || typeof text !== 'string') return false;
  return /\$[\s\S]*?\$/.test(text);
}

/**
 * Utility function to extract math expressions from text
 */
export function extractMathExpressions(text) {
  if (!text || typeof text !== 'string') return [];
  
  const expressions = [];
  const mathRegex = /(\$\$[\s\S]*?\$\$|\$[^$\n]*?\$)/g;
  let match;
  
  while ((match = mathRegex.exec(text)) !== null) {
    expressions.push(match[1]);
  }
  
  return expressions;
}

export default MathTextRenderer;
