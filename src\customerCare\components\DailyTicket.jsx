import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Container,
  Row,
  Col,
  Form,
  Pagination,
  Dropdown,
  DropdownButton,
  Modal,
} from "react-bootstrap";
import dailyTickets from "../utils/dummyTickets";

const DailyTicket = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState(3);
  const [currentPage, setCurrentPage] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [currentTicketId, setCurrentTicketId] = useState(null);
  const [reply, setReply] = useState("");
  const [attachment, setAttachment] = useState(null);

  const filteredTickets = dailyTickets.filter((ticket) =>
    ticket.problem.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalPages = Math.ceil(filteredTickets.length / itemsPerPage);

  const displayedTickets = filteredTickets.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleResolveClick = (ticketId) => {
    setCurrentTicketId(ticketId);
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setReply("");
    setAttachment(null);
  };

  const handleFormSubmit = () => {
    console.log("Reply:", reply);
    console.log("Attachment:", attachment);
    console.log("Resolved Ticket ID:", currentTicketId);

    // Reset form and close modal
    setReply("");
    setAttachment(null);
    setShowModal(false);
  };

  return (
    <Container className="mt-2">
      <Row className="justify-content-center align-items-center">
        <Col xs={12} md={8}>
          <h1>Daily Tickets ({filteredTickets.length})</h1>
        </Col>
      </Row>

      {/* Search and Items Per Page Dropdown */}
      <Row className="justify-content-center align-items-center mt-2">
        <Col xs={7} md={5}>
          <Form.Control
            type="text"
            placeholder="Search by problem"
            value={searchQuery}
            onChange={handleSearchChange}
          />
        </Col>
        <Col xs={5} md={3}>
          <DropdownButton
            id="dropdown-basic-button"
            variant="success"
            title={
              <>
                <span className="d-none d-md-inline">
                  Tickets per page {itemsPerPage}
                </span>
                <span className="d-md-none">Per page {itemsPerPage}</span>
              </>
            }
            onSelect={(e) => handleItemsPerPageChange(e)}
          >
            {[1, 2, 3, 6, 9, 18, 60, 99].map((number) => (
              <Dropdown.Item key={number} eventKey={number}>
                {number}
              </Dropdown.Item>
            ))}
          </DropdownButton>
        </Col>
      </Row>

      {/* Ticket Cards */}
      <Row className="justify-content-center align-items-center mt-4">
        {displayedTickets.map((ticket) => (
          <Col key={ticket.id} xs={12} lg={8} className="mb-4">
            <Card className="shadow-sm border-2">
              <Card.Body>
                <Card.Title as="h5" className="fw-bold text-dark">
                  {ticket.problem}
                </Card.Title>
                <Card.Subtitle className="mb-2 text-muted">
                  Asked by <strong>{ticket.student.name}</strong> at{" "}
                  {ticket.timing}
                </Card.Subtitle>
                <Card.Text className="text-muted">
                  <small>{ticket.student.email}</small>
                </Card.Text>
                <div className="d-flex justify-content-end">
                  <Button
                    variant="success"
                    size="sm"
                    onClick={() => handleResolveClick(ticket.id)}
                  >
                    Resolve
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Pagination */}
      <Row className="justify-content-center">
        <Col xs="auto">
          <Pagination className="pagination-success">
            <Pagination.Prev
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            />
            {[...Array(totalPages).keys()].map((number) => (
              <Pagination.Item
                variant="success"
                key={number + 1}
                active={number + 1 === currentPage}
                onClick={() => handlePageChange(number + 1)}
              >
                {number + 1}
              </Pagination.Item>
            ))}
            <Pagination.Next
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            />
          </Pagination>
        </Col>
      </Row>

      {/* Resolve Ticket Modal */}
      <Modal show={showModal} onHide={handleModalClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>Resolve Ticket</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Reply</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={reply}
                onChange={(e) => setReply(e.target.value)}
                placeholder="Enter your reply"
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Attachments (Optional)</Form.Label>
              <Form.Control
                type="file"
                onChange={(e) => setAttachment(e.target.files[0])}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleModalClose}>
            Close
          </Button>
          <Button variant="success" onClick={handleFormSubmit}>
            Submit
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default DailyTicket;
