import React from "react";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getCustomerProfile, editCustomerProfile, deleteCustomerProfile } from "../../redux/slice/customerCareSlice";
import { Card, Button, Modal, Form, Container, Row, Col } from "react-bootstrap";
import Swal from "sweetalert2";
import toast from "react-hot-toast";
import CustomerDashboard from "../components/CustomerDashboard";
import CustomerCareDashboard from "./CustomerCareDashboard";
import NavigationBar from "../../commonComponents/NavigationBar";
import { FaUser, FaUserAlt, FaEnvelope, FaPhone } from "react-icons/fa";
import { BsPencilSquare, BsTrash } from "react-icons/bs";
import Skeleton from "react-loading-skeleton";
import 'react-loading-skeleton/dist/skeleton.css';

const CustomerProfile = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const access = useSelector((state) => state?.customerCare?.access);

  useEffect(() => {
    if (!access || access === null) { // Check for both null and undefined
      toast.error('Please log in as a customer care!');
      const timer = setTimeout(() => {
        navigate('/customer_care_login'); // Redirect to login after 2 seconds
      }, 1000);
      return () => clearTimeout(timer); // Clean up the timer
    }
  }, [access, navigate]);


  const [loading, setLoading] = useState(true);
  const [editLoading, setEditLoading] = useState(false);
  const [customer, setCustomer] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [profileData, setProfileData] = useState({
    username: "",
    email: "",
    first_name: "",
    last_name: "",
    password: "",
    contact: "",
  });

  const fetchCustomerProfile = async () => {
    try {
      const response = await dispatch(getCustomerProfile());
      setCustomer(response.payload);
      setProfileData({
        username: response.payload?.user.username,
        email: response.payload?.user.email,
        first_name: response.payload?.user.first_name,
        last_name: response.payload?.user.last_name,
        password: "",
        contact: response.payload?.contact,
      });
    } catch (error) {
      console.error("Error fetching customer profile:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustomerProfile();
  }, []);

  const handleEdit = async () => {
    setEditLoading(true);
    try {
      const updatedProfile = {
        user: {
          email: profileData.email,
          first_name: profileData.first_name,
          last_name: profileData.last_name,
          ...(profileData.password && { password: profileData.password }), // Only include password if not empty
        },
        contact: profileData.contact,
        account_status: customer?.account_status,
      };

      // Dispatch the editCustomerProfile action and unwrap the result
      const resultAction = await dispatch(editCustomerProfile({ updatedProfile })).unwrap();

      setShowEditModal(false); // Close modal on success
      toast.success("Profile updated successfully!");
      fetchCustomerProfile(); // Reload customer profile
    } catch (error) {
      console.error("Error editing customer profile:", error);
      toast.error("Failed to update profile.");
    } finally {
      setEditLoading(false);
    }
  };

  const handleDelete = () => {
    Swal.fire({
      title: "Are you sure?",
      text: "This action will delete your profile permanently!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          await dispatch(deleteCustomerProfile()).unwrap();

          Swal.fire("Deleted!", "Your profile has been deleted.", "success").then(() => {
            navigate("/customer_care_login");
          });
        } catch (error) {
          console.error("Error deleting customer profile:", error);
          Swal.fire("Error", "There was an issue deleting your profile.", "error");
        }
      }
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setProfileData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  return (
    <>
      <NavigationBar />
      <Container>
        <Row className="justify-content-center align-items-center mt-3" style={{ minHeight: "100vh" }}>
          <Col md={12} className="mt-4 mb-4">
            <Card className="shadow-sm rounded p-4 p-md-2">
              <Card.Body className="d-md-flex flex-wrap justify-content-between align-items-center">
                <div className="text-center mb-4 mb-md-0">
                  {loading ? (
                    <Skeleton width={150} height={30} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                  ) : (
                    <h4 className="text-black fw-medium">                     
                      Hi, <strong className="text-primary fw-bold">{customer?.user?.first_name} {customer?.user?.last_name} </strong>
                    </h4>
                  )}
                </div>
                <div className="mb-3 mb-md-0 d-flex gap-2 align-items-center">
                  {loading ? (
                    <Skeleton width={200} height={20} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                  ) : (
                    <>
                      <FaUserAlt className="text-success" />
                      <span> {customer?.user?.username} </span>
                    </>
                  )}
                </div>
                <div className="mb-3 mb-md-0 d-flex gap-2 align-items-center">
                  {loading ? (
                    <Skeleton width={250} height={20} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                  ) : (
                    <>
                      <FaEnvelope className="text-success" />
                      <span>{customer?.user?.email}</span>
                    </>
                  )}
                </div>
                <div className="mb-3 mb-md-0 d-flex gap-2 align-items-center">
                  {loading ? (
                    <Skeleton width={150} height={20} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                  ) : (
                    <>
                      <FaPhone className="text-success" />
                      <span>{customer?.contact}</span>
                    </>
                  )}
                </div>
                <div className="d-flex gap-2 align-items-center">
                  <Button
                    title="edit profile"
                    variant="outline-success"
                    className="d-flex gap-2 align-items-center justify-content-center"
                    onClick={() => setShowEditModal(true)}
                    disabled={loading}
                  >
                    <BsPencilSquare />
                  </Button>
                  <Button
                    variant="outline-danger"
                    onClick={handleDelete}
                    className="d-flex gap-2 align-items-center justify-content-center"
                    title="delete profile"
                    disabled={loading}
                  >
                    <BsTrash />
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col xs={12}>
            <CustomerCareDashboard />
          </Col>

          <Col xs={12}>
            <CustomerDashboard />
          </Col>
        </Row>

        {/* Edit Profile Modal */}
        <Modal show={showEditModal} centered onHide={() => setShowEditModal(false)}>
          <Modal.Header closeButton>
            <Modal.Title>Edit Profile</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form>
              <Row>
                <Col xs={12} md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Email</Form.Label>
                    <Form.Control
                      type="email"
                      name="email"
                      value={profileData.email}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
                <Col xs={12} md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>First Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="first_name"
                      value={profileData.first_name}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
                <Col xs={12} md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Last Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="last_name"
                      value={profileData.last_name}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
                <Col xs={12} md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Contact</Form.Label>
                    <Form.Control
                      type="text"
                      name="contact"
                      value={profileData.contact}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
                <Col xs={12} md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Password</Form.Label>
                    <Form.Control
                      type="password"
                      name="password"
                      value={profileData.password}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
              </Row>
              <Button
                variant="success"
                onClick={handleEdit}
                className="w-100 mt-3"
                disabled={editLoading || loading}
              >
                {editLoading ? "Saving..." : "Save Changes"}
              </Button>
            </Form>
          </Modal.Body>
        </Modal>
      </Container>
    </>
  );
};

export default CustomerProfile;
