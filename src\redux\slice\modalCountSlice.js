import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get auth token
const getAuthToken = (getState) => {
  const { access } = getState().customerCare;
  return access;
};

// Base API URL for modal count
const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MODAL_COUNT_ENDPOINT}`;

// Initial state
const initialState = {
  isLoading: false,
  error: null,
  data: null, // Added to store modal counts
};

//  Get modal counts (GET)
export const getModalCounts = createAsyncThunk(
  "modalCounts/fetch",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(API_URL, { // Updated to store response
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data; // Return response data
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching modal counts");
    }
  }
);

// Slice
const modalCountSlice = createSlice({
  name: "modalCounts",
  initialState,
  reducers: {
    clearModalCountError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getModalCounts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getModalCounts.fulfilled, (state, action) => {
        state.isLoading = false;
         state.error = null;
        // Removed logic to store fetched data
      })
      .addCase(getModalCounts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Actions
export const { clearModalCountError } = modalCountSlice.actions;

// Reducer
export default modalCountSlice.reducer;
