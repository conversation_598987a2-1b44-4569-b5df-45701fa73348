import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get authToken from Redux state
const getAuthToken = (getState) => {
  const { access } = getState().customerCare; // Adjust state path as needed
  return access;
};

// Create sign-up content
export const createSignUpContent = createAsyncThunk(
  "signupContent/create",
  async ({ formData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SIGNUP_CONTENT}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to create content");
    }
  }
);

// Get all sign-up contents
export const getSignUpContents = createAsyncThunk(
  "signupContent/getAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SIGNUP_CONTENT}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch content");
    }
  }
);

// Edit sign-up content
export const editSignUpContent = createAsyncThunk(
  "signupContent/edit",
  async ({ id, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SIGNUP_CONTENT_DETAILS}${id}/`,
        updatedData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to update content");
    }
  }
);

// Delete sign-up content
export const deleteSignUpContent = createAsyncThunk(
  "signupContent/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SIGNUP_CONTENT_DETAILS}${id}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to delete content");
    }
  }
);

// Slice
const signUpContentSlice = createSlice({
  name: "signupContent",
  initialState: {
    signupContents: [],
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get all content
      .addCase(getSignUpContents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSignUpContents.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(getSignUpContents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create content
      .addCase(createSignUpContent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSignUpContent.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(createSignUpContent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Edit content
      .addCase(editSignUpContent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(editSignUpContent.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(editSignUpContent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete content
      .addCase(deleteSignUpContent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSignUpContent.fulfilled, (state, action) => {
        state.loading = false; 
        state.error = null;       
      })
      .addCase(deleteSignUpContent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default signUpContentSlice.reducer;
