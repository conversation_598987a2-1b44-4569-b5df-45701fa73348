import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  InputGroup,
  FormControl,
  Pagination,
  Container,
  Dropdown,
  Card,
  Form,
  DropdownButton,
} from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchBanners,
  createBanner,
  deleteBanner,
  updateBanner,
} from "../../redux/slice/bannerSlice";
import UploadBannerCard from "../components/UploadBannerCard";
import BannerCard from "../components/BannerCard";
import NavigationBar from "../../commonComponents/NavigationBar";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import Swal from "sweetalert2";

const BannerDashboard = () => {
  const dispatch = useDispatch();
  const [allBanners, setAllBanners] = useState([]);
  const [filteredBanners, setFilteredBanners] = useState([]);
  const [newBanner, setNewBanner] = useState({
    banner_name: "",
    banner_image: null,
  });
  const [search, setSearch] = useState("");
  // const [bannersPerPage, setBannersPerPage] = useState(6);
  const [itemsPerPage, setItemsPerPage] = useState(6);
  const [currentPage, setCurrentPage] = useState(1);
  const error = useSelector((state) => state.banners.error);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  const fetchBannersData = async () => {
    try {
      setIsLoading(true);
      const response = await dispatch(fetchBanners());
      if (response?.payload) {
        const banners = Array.isArray(response.payload) ? response.payload : [];
        setAllBanners(banners);
        filterBanners(banners);
      }
    } catch (error) {
      console.error("Error fetching banners:", error);
      toast.error("Failed to load banners");
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    fetchBannersData();
  }, [dispatch]);  
  

  const handleAddBanner = async (e, resetImagePreview) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("banner_name", newBanner?.banner_name);
    if (newBanner?.banner_image) {
      formData.append("banner_image", newBanner.banner_image);
    }
  
    try {
      const response = await dispatch(createBanner(formData));
  
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Banner added successfully!");
        setNewBanner({ banner_name: "", banner_image: null });
        resetImagePreview(); // Reset image preview & input field
        fetchBannersData(); // Refresh banners list
      } else {
        toast.error("Failed to add banner");
      }
    } catch (error) {
      console.error("Error adding banner:", error);
      toast.error(error?.message || "Failed to add banner. Please try again!");
    }
  };
  
  

  const handleDeleteBanner = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });
  
    if (result?.isConfirmed) {
      try {
        const response = await dispatch(deleteBanner(id));
  
        if (response?.meta?.requestStatus === "fulfilled") {
          toast.success("Banner deleted successfully!");
          fetchBannersData(); // Refresh banners after deletion
        } else {
          toast.error("Failed to delete banner");
        }
      } catch (error) {
        console.error("Error deleting banner:", error);
        toast.error("Failed to delete banner");
      }
    }
  };
  
  const handleUpdateBanner = async (id, updatedData) => {
    try {
      const formData = new FormData();
      formData.append("banner_name", updatedData?.banner_name);
      if (updatedData?.banner_image) {
        formData.append("banner_image", updatedData.banner_image);
      }
  
      const response = await dispatch(updateBanner({ id, formData }));
  
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Banner updated successfully!");
        fetchBannersData(); // Refresh the banners
      } else {
        toast.error("Failed to update banner");
      }
    } catch (error) {
      console.error("Error updating banner:", error);
      toast.error("Failed to update banner");
    }
  };
  

  const filterBanners = (banners) => {
    const filtered = Array.isArray(banners)
      ? banners.filter((banner) =>
          banner?.banner_name?.toLowerCase().includes(searchTerm?.toLowerCase())
        )
      : [];
    setFilteredBanners(filtered);
  };

  // Refactor to filter questions on searchTerm change
  useEffect(() => {
    filterBanners(allBanners);
  }, [searchTerm, allBanners]);

  const paginate = (array, pageSize, pageNumber) => {
    const offset = (pageNumber - 1) * pageSize;
    return array.slice(offset, offset + pageSize);
  };

  const handleDropdownChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  const paginatedBanners = paginate(
    filteredBanners,
    itemsPerPage,
    currentPage
  );

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const totalPages = Math.ceil(filteredBanners.length / itemsPerPage);

  const indexOfLastBanner = currentPage * itemsPerPage;
  const indexOfFirstBanner = indexOfLastBanner - itemsPerPage;
  const currentBanners = filteredBanners.slice(
    indexOfFirstBanner,
    indexOfLastBanner
  );



  return (
    <>
      <NavigationBar />
      <Container>
        <Row className="mt-5">
          <Col md={4}>
            <UploadBannerCard
              newBanner={newBanner}
              setNewBanner={setNewBanner}
              handleInputChange={(e) =>
                setNewBanner({ ...newBanner, [e.target.name]: e.target.value })
              }
              handleFileChange={(e) =>
                setNewBanner({ ...newBanner, banner_image: e.target.files[0] })
              }
              handleAddBanner={handleAddBanner}
            />
            {/* Removed direct error display */}
            {error && toast.error("Something went wrong!")}
          </Col>

          <Col md={8}>
            <div className="d-flex justify-content-center mb-3">
              <Form.Control
                type="text"
                placeholder="Search by name"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ width: "60%" }}
              />
              <DropdownButton
                id="dropdown-basic-button"
                title={`Banners per page: ${itemsPerPage}`}
                variant="success"
                className="mx-1"
                onSelect={handleDropdownChange}
              >
                {[2, 6, 8, 16, 32, 64, 100].map((number) => (
                  <Dropdown.Item key={number} eventKey={number}>
                    {number}
                  </Dropdown.Item>
                ))}
              </DropdownButton>
            </div>
            {isLoading ? (
              <Row>
                {[...Array(itemsPerPage)].map((_,index) => (
                  <Col key={index} md={6} className="mb-3">
                    <Card className="position-relative rounded-3 shadow mb-3">
                      <Skeleton
                        height={200}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                        className="rounded-3"
                      />
                      <Card.Body
                        className="position-absolute top-0 start-0 end-0 bottom-0 d-flex justify-content-center align-items-center p-3 text-white rounded-3"
                        style={{
                          backgroundColor: "rgba(0, 0, 0, 0.5)",
                          opacity: 0,
                          transition: "opacity 0.3s ease-in-out",
                        }}
                      >
                        <div className="d-flex flex-column align-items-center">
                          <Skeleton
                            width="70%"
                            height={20}
                            baseColor="#e6ffe6"
                            highlightColor="#c4f7c4"
                          />
                          <div className="d-flex justify-content-center w-100 mt-2">
                            <Skeleton
                              width={60}
                              height={30}
                              baseColor="#e6ffe6"
                              highlightColor="#c4f7c4"
                              className="m-1"
                            />
                            <Skeleton
                              width={60}
                              height={30}
                              baseColor="#e6ffe6"
                              highlightColor="#c4f7c4"
                              className="m-1"
                            />
                          </div>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            ) : (
              <Row>
                {
                  paginatedBanners.map((banner) => (
                    <Col key={banner.id} md={6} className="mb-3">
                      <BannerCard
                        banner={banner}
                        handleDeleteBanner={handleDeleteBanner}
                        handleUpdateBanner={handleUpdateBanner}
                      />
                    </Col>
                  ))}
                 
              </Row>
            )}
            <div className="d-flex justify-content-center mt-1 mb-3">
              <PaginationComponent
                totalPages={totalPages}
                currentPage={currentPage}
                handlePageChange={handlePageChange}
              />
            </div>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default BannerDashboard;
