import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { createSignUpContent, getSignUpContents, editSignUpContent, deleteSignUpContent } from "../../redux/slice/signupContentSlice";
import SignUpContentForm from "../components/SignUpContentForm";
import SignUpContentCard from "../components/SignUpContentCard";
import SignUpContentModal from "../components/SignUpContentModal";
import SignUpContentViewModal from "../components/SignUpContentViewModal";
import { Container, Row, Col, Form } from "react-bootstrap";
import Swal from "sweetalert2";
import NavigationBar from "../../commonComponents/NavigationBar";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import { Dropdown, DropdownButton } from "react-bootstrap";

const SignUpContentDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading } = useSelector((state) => state.signupContents);
  const { access } = useSelector((state) => state.customerCare); // Access from store
  const [signupContents, setSignUpContents] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedContent, setSelectedContent] = useState(null);
  const [modalType, setModalType] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);
  const [isModalVisible, setIsModalVisible] = useState(false); // Track modal visibility

  const fetchSignUpContents = async () => {
    const response = await dispatch(getSignUpContents())?.unwrap();
    setSignUpContents(response);
  };

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    fetchSignUpContents();
  }, [dispatch, access, navigate]);

  const handleCreate = async (formData) => {
    const resultAction = await dispatch(createSignUpContent({ formData }));
    if (resultAction?.meta?.requestStatus === "fulfilled") {
      toast.success("Content created successfully!");
      fetchSignUpContents(); // Fetch contents again after successful creation
    } else {
      toast.error("Failed to create content. Please try again.");
    }
  };

  const handleEdit = (content) => {
    setSelectedContent(content);
    setModalType("edit");
    setIsModalVisible(true); // Show the modal
  };

  const handleView = (content) => {
    setSelectedContent(content);
    setModalType("view");
  };

  const handleDelete = (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then((result) => {
      if (result.isConfirmed) {
        dispatch(deleteSignUpContent(id)).then((resultAction) => {
          if (resultAction?.meta?.requestStatus === "fulfilled") {
            toast.success("Content deleted successfully!");
            fetchSignUpContents(); // Fetch contents again after successful deletion
          } else {
            toast.error("Failed to delete content. Please try again.");
          }
        });
      }
    });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to the first page
  };

  const paginatedContents = signupContents.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <>
      <NavigationBar />
      <Container className="mt-4">
        <Row>
          <Col md={4} className="mb-3">
            <SignUpContentForm onSubmit={handleCreate} setSearchTerm={setSearchTerm} />
          </Col>
          <Col md={8} className="mt-3 mt-md-0">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <Form.Control
                type="text"
                placeholder="Search content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="me-2"
                style={{ flex: 1 }}
              />
              <DropdownButton
                id="dropdown-basic-button"
                title={`${itemsPerPage} per page`}
                variant="primary"
                onSelect={(e) => handleItemsPerPageChange(Number(e))}
              >
                {[6, 12, 16, 32, 64, 100].map((value) => (
                  <Dropdown.Item key={value} eventKey={value}>
                  Per page {value} 
                  </Dropdown.Item>
                ))}
              </DropdownButton>
            </div>
            {loading ? (
              <Row>
                {Array.from({ length: 6 }).map((_, index) => (
                  <Col md={6} key={index} className="mb-3">
                    <Skeleton 
                      height={150} 
                      baseColor="#e6ffe6" 
                      highlightColor="#c4f7c4" 
                    />
                  </Col>
                ))}
              </Row>
            ) : (
              <Row>
                {Array.isArray(paginatedContents) && paginatedContents
                  ?.filter((content) => content?.heading?.toLowerCase()?.includes(searchTerm?.toLowerCase()))
                  ?.map((content) => (
                    <Col md={6} key={content?.id}>
                      <SignUpContentCard 
                        content={content} 
                        onView={() => handleView(content)}
                        onEdit={() => handleEdit(content)} 
                        onDelete={() => handleDelete(content?.id)} 
                      />
                    </Col>
                  ))}
              </Row>
            )}
            <PaginationComponent
              totalPages={Math.ceil(signupContents.length / itemsPerPage)}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          </Col>
        </Row>

        {modalType === "edit" && (
          <SignUpContentModal 
            show={isModalVisible} // Pass the visibility state
            content={selectedContent} 
            onClose={() => setIsModalVisible(false)} // Close the modal
            onSubmit={(updatedContent) => {
              dispatch(editSignUpContent({ 
                id: selectedContent?.id, 
                updatedData: updatedContent 
              })).then((resultAction) => {
                if (resultAction?.meta?.requestStatus === "fulfilled") {
                  toast.success("Content updated successfully!");
                  fetchSignUpContents(); // Refresh the content list
                } else {
                  toast.error("Failed to update content. Please try again.");
                }
              });
              setIsModalVisible(false); // Close the modal after submission
            }} 
          />
        )}

        {modalType === "view" && (
          <SignUpContentViewModal 
            content={selectedContent} 
            onClose={() => setModalType("")} 
          />
        )}
      </Container>
    </>
  );
};

export default SignUpContentDashboard;
