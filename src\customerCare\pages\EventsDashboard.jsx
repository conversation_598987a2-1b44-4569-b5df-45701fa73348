import React, { useState, useEffect } from "react";
import { <PERSON>, But<PERSON>, Card, Container, Row, Col, Modal, Dropdown, Badge } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import {
  createEvent,
  getAllEvents,
  updateEvent,
  deleteEvent,
} from "../../redux/slice/eventsSlice";
import NavigationBar from "../../commonComponents/NavigationBar";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import { FaCalendarAlt, FaMapMarkerAlt, FaClock, FaUsers, FaTag } from 'react-icons/fa';

const EventsDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { access } = useSelector((state) => state.customerCare);
  const { events, isLoading } = useSelector((state) => state.events);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    date: "",
    time: "",
    location: "",
    category: "academic",
    attendees: "",
    maxAttendees: "",
    organizer: "",
    registrationRequired: false,
    status: "upcoming",
    tags: [],
    image: null,
  });

  const [editingEvent, setEditingEvent] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showEditModal, setShowEditModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [tagInput, setTagInput] = useState("");

  const [itemsPerPage, setItemsPerPage] = useState(6);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const categories = [
    "academic",
    "cultural", 
    "technology",
    "sports",
    "professional",
    "networking"
  ];

  const statusOptions = [
    "upcoming",
    "ongoing", 
    "completed"
  ];

  const fetchEventsData = async () => {
    try {
      await dispatch(getAllEvents()).unwrap();
    } catch (err) {
      toast.error("Failed to fetch events.");
    }
  };

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    const fetchData = async () => {
      setIsFetching(true);
      await fetchEventsData();
      setIsFetching(false);
    };
    fetchData();
  }, [access, dispatch, navigate]);

  const filteredEvents = events.filter((event) =>
    event.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.category?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.location?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    setTotalPages(Math.ceil(filteredEvents.length / itemsPerPage));
  }, [filteredEvents, itemsPerPage]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleImageChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      image: e.target.files[0],
    }));
  };

  const handleTagAdd = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput("");
    }
  };

  const handleTagRemove = (tagToRemove) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    const payload = {
      ...formData,
      attendees: parseInt(formData.attendees) || 0,
      maxAttendees: parseInt(formData.maxAttendees) || 0,
    };

    try {
      const result = await dispatch(createEvent(payload));
      if (result.meta.requestStatus === "fulfilled") {
        toast.success("Event created successfully!");
        fetchEventsData();
        setFormData({
          title: "",
          description: "",
          date: "",
          time: "",
          location: "",
          category: "academic",
          attendees: "",
          maxAttendees: "",
          organizer: "",
          registrationRequired: false,
          status: "upcoming",
          tags: [],
          image: null,
        });
        setTagInput("");
      } else {
        toast.error("Failed to create event.");
      }
    } catch (err) {
      toast.error("An error occurred.");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (event) => {
    setEditingEvent(event);
    setFormData({
      title: event.title || "",
      description: event.description || "",
      date: event.date || "",
      time: event.time || "",
      location: event.location || "",
      category: event.category || "academic",
      attendees: event.attendees || "",
      maxAttendees: event.maxAttendees || "",
      organizer: event.organizer || "",
      registrationRequired: event.registrationRequired || false,
      status: event.status || "upcoming",
      tags: event.tags || [],
      image: null, // Don't pre-fill image for editing
    });
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setEditingEvent(null);
    setFormData({
      title: "",
      description: "",
      date: "",
      time: "",
      location: "",
      category: "academic",
      attendees: "",
      maxAttendees: "",
      organizer: "",
      registrationRequired: false,
      status: "upcoming",
      tags: [],
      image: null,
    });
    setTagInput("");
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    
    const payload = {
      ...formData,
      attendees: parseInt(formData.attendees) || 0,
      maxAttendees: parseInt(formData.maxAttendees) || 0,
    };

    try {
      const result = await dispatch(updateEvent({ id: editingEvent.id, updatedData: payload }));
      if (result.meta.requestStatus === "fulfilled") {
        toast.success("Event updated successfully!");
        fetchEventsData();
        handleCloseEditModal();
      } else {
        toast.error("Failed to update event.");
      }
    } catch (err) {
      toast.error("An error occurred.");
    }
  };

  const handleDelete = (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "This will permanently delete the event!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const resultAction = await dispatch(deleteEvent(id));
          if (resultAction.meta.requestStatus === "fulfilled") {
            toast.success("Event deleted successfully!");
            fetchEventsData();
          } else {
            toast.error("Failed to delete event.");
          }
        } catch {
          toast.error("An error occurred.");
        }
      }
    });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const paginatedEvents = filteredEvents.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getCategoryColor = (category) => {
    const colors = {
      academic: "primary",
      cultural: "success",
      technology: "danger",
      sports: "warning",
      professional: "info",
      networking: "dark"
    };
    return colors[category] || "secondary";
  };

  const getStatusColor = (status) => {
    const colors = {
      upcoming: "primary",
      ongoing: "success",
      completed: "secondary"
    };
    return colors[status] || "secondary";
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <>
      <NavigationBar />
      <Container className="my-5">
        <Row>
          <Col md={4}>
            <Card className="shadow">
              <Card.Body>
                <Card.Title className="text-center text-success mb-4">
                  <FaCalendarAlt className="me-2" />
                  Add Event
                </Card.Title>
                <Form onSubmit={handleSubmit}>
                  <Form.Group className="mb-3">
                    <Form.Label>Event Title</Form.Label>
                    <Form.Control
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Date</Form.Label>
                        <Form.Control
                          type="date"
                          name="date"
                          value={formData.date}
                          onChange={handleChange}
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Time</Form.Label>
                        <Form.Control
                          type="time"
                          name="time"
                          value={formData.time}
                          onChange={handleChange}
                          required
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Location</Form.Label>
                    <Form.Control
                      type="text"
                      name="location"
                      value={formData.location}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Category</Form.Label>
                    <Form.Select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                    >
                      {categories.map(cat => (
                        <option key={cat} value={cat}>
                          {cat.charAt(0).toUpperCase() + cat.slice(1)}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Organizer</Form.Label>
                    <Form.Control
                      type="text"
                      name="organizer"
                      value={formData.organizer}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Current Attendees</Form.Label>
                        <Form.Control
                          type="number"
                          name="attendees"
                          value={formData.attendees}
                          onChange={handleChange}
                          min="0"
                          placeholder="0"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Max Attendees</Form.Label>
                        <Form.Control
                          type="number"
                          name="maxAttendees"
                          value={formData.maxAttendees}
                          onChange={handleChange}
                          min="1"
                          required
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Status</Form.Label>
                    <Form.Select
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                    >
                      {statusOptions.map(status => (
                        <option key={status} value={status}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Check
                      type="checkbox"
                      name="registrationRequired"
                      checked={formData.registrationRequired}
                      onChange={handleChange}
                      label="Registration Required"
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Tags</Form.Label>
                    <div className="d-flex mb-2">
                      <Form.Control
                        type="text"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        placeholder="Add a tag"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleTagAdd())}
                      />
                      <Button
                        variant="outline-success"
                        size="sm"
                        className="ms-2"
                        onClick={handleTagAdd}
                        type="button"
                      >
                        Add
                      </Button>
                    </div>
                    <div>
                      {formData.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          bg="secondary"
                          className="me-2 mb-2"
                          style={{ cursor: 'pointer' }}
                          onClick={() => handleTagRemove(tag)}
                        >
                          <FaTag className="me-1" />
                          {tag} ×
                        </Badge>
                      ))}
                    </div>
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Event Image</Form.Label>
                    <Form.Control
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                    />
                  </Form.Group>

                  <div className="d-grid">
                    <Button variant="success" type="submit" disabled={loading}>
                      {loading ? "Creating..." : "Create Event"}
                    </Button>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Col>

          <Col md={8}>
            <Row className="my-4 align-items-center">
              <Col xs={8} sm={9}>
                <Form.Control
                  type="text"
                  placeholder="Search events by title, category, or location..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Col>
              <Col xs={4} sm={3} className="text-end">
                <Dropdown>
                  <Dropdown.Toggle variant="success" id="itemsPerPageDropdown" className="w-100">
                    {itemsPerPage === filteredEvents.length ? "All" : `${itemsPerPage} per page`}
                  </Dropdown.Toggle>
                  <Dropdown.Menu>
                    <Dropdown.Item onClick={() => handleItemsPerPageChange(6)}>
                      6 per page
                    </Dropdown.Item>
                    <Dropdown.Item onClick={() => handleItemsPerPageChange(12)}>
                      12 per page
                    </Dropdown.Item>
                    <Dropdown.Item onClick={() => handleItemsPerPageChange(24)}>
                      24 per page
                    </Dropdown.Item>
                    <Dropdown.Item onClick={() => handleItemsPerPageChange(filteredEvents.length)}>
                      All
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row>
              {isFetching
                ? Array.from({ length: 6 }).map((_, index) => (
                    <Col md={6} lg={4} key={index} className="mb-4">
                      <Skeleton
                        height={300}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                      />
                    </Col>
                  ))
                : paginatedEvents.map((event) => (
                    <Col md={6} lg={4} key={event.id} className="mb-4">
                      <Card className="shadow h-100">
                        {event.image && (
                          <Card.Img 
                            variant="top" 
                            src={event.image} 
                            style={{ height: '200px', objectFit: 'cover' }}
                          />
                        )}
                        <Card.Body className="d-flex flex-column">
                          <div className="mb-2">
                            <Badge bg={getCategoryColor(event.category)} className="me-2">
                              {event.category?.toUpperCase()}
                            </Badge>
                            <Badge bg={getStatusColor(event.status)}>
                              {event.status?.toUpperCase()}
                            </Badge>
                          </div>
                          
                          <Card.Title className="text-truncate" title={event.title}>
                            {event.title}
                          </Card.Title>
                          
                          <Card.Text className="text-muted small flex-grow-1">
                            {event.description?.length > 100
                              ? `${event.description.substring(0, 100)}...`
                              : event.description}
                          </Card.Text>

                          <div className="mb-3">
                            <div className="d-flex align-items-center mb-1">
                              <FaCalendarAlt className="text-muted me-2" size={12} />
                              <small>{formatDate(event.date)}</small>
                            </div>
                            <div className="d-flex align-items-center mb-1">
                              <FaClock className="text-muted me-2" size={12} />
                              <small>{event.time}</small>
                            </div>
                            <div className="d-flex align-items-center mb-1">
                              <FaMapMarkerAlt className="text-muted me-2" size={12} />
                              <small>{event.location}</small>
                            </div>
                            <div className="d-flex align-items-center">
                              <FaUsers className="text-muted me-2" size={12} />
                              <small>{event.attendees}/{event.maxAttendees} attendees</small>
                            </div>
                          </div>

                          <div className="d-flex justify-content-center mt-auto">
                            <Button
                              variant="outline-primary"
                              size="sm"
                              className="me-2"
                              onClick={() => handleEdit(event)}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="outline-danger"
                              size="sm"
                              onClick={() => handleDelete(event.id)}
                            >
                              Delete
                            </Button>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  ))}
            </Row>

            {filteredEvents.length === 0 && !isFetching && (
              <div className="text-center py-5">
                <FaCalendarAlt size={64} className="text-muted mb-3" />
                <h5 className="text-muted">No events found</h5>
                <p className="text-muted">Try adjusting your search or create a new event.</p>
              </div>
            )}

            <PaginationComponent
              totalPages={totalPages}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          </Col>
        </Row>
      </Container>

      {/* Edit Event Modal */}
      <Modal show={showEditModal} onHide={handleCloseEditModal} centered size="lg">
        <Modal.Header closeButton>
          <Modal.Title className="text-primary">
            <FaCalendarAlt className="me-2" />
            Edit Event
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Event Title</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Category</Form.Label>
                  <Form.Select
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                  >
                    {categories.map(cat => (
                      <option key={cat} value={cat}>
                        {cat.charAt(0).toUpperCase() + cat.slice(1)}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
              />
            </Form.Group>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Date</Form.Label>
                  <Form.Control
                    type="date"
                    name="date"
                    value={formData.date}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Time</Form.Label>
                  <Form.Control
                    type="time"
                    name="time"
                    value={formData.time}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Status</Form.Label>
                  <Form.Select
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                  >
                    {statusOptions.map(status => (
                      <option key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Location</Form.Label>
                  <Form.Control
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Organizer</Form.Label>
                  <Form.Control
                    type="text"
                    name="organizer"
                    value={formData.organizer}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Current Attendees</Form.Label>
                  <Form.Control
                    type="number"
                    name="attendees"
                    value={formData.attendees}
                    onChange={handleChange}
                    min="0"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Max Attendees</Form.Label>
                  <Form.Control
                    type="number"
                    name="maxAttendees"
                    value={formData.maxAttendees}
                    onChange={handleChange}
                    min="1"
                    required
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Check
                type="checkbox"
                name="registrationRequired"
                checked={formData.registrationRequired}
                onChange={handleChange}
                label="Registration Required"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Tags</Form.Label>
              <div className="d-flex mb-2">
                <Form.Control
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder="Add a tag"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleTagAdd())}
                />
                <Button
                  variant="outline-success"
                  className="ms-2"
                  onClick={handleTagAdd}
                  type="button"
                >
                  Add
                </Button>
              </div>
              <div>
                {formData.tags.map((tag, index) => (
                  <Badge
                    key={index}
                    bg="secondary"
                    className="me-2 mb-2"
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleTagRemove(tag)}
                  >
                    <FaTag className="me-1" />
                    {tag} ×
                  </Badge>
                ))}
              </div>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Event Image</Form.Label>
              <Form.Control
                type="file"
                accept="image/*"
                onChange={handleImageChange}
              />
              <Form.Text className="text-muted">
                Leave empty to keep current image
              </Form.Text>
            </Form.Group>

            <div className="d-grid">
              <Button variant="primary" type="submit">
                Update Event
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default EventsDashboard;
