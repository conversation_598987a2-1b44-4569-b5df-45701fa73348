import React from "react";
import {
  <PERSON>,
  Button,
  ButtonGroup,
  Form,
  Badge,
  Accordion,
} from "react-bootstrap";
import {
  FaQuestionCircle,
  FaRegCircle,
  FaInfoCircle,
  FaClipboardList,
  FaCalendarAlt,
} from "react-icons/fa";
import MathTextRenderer from "../../commonComponents/MathTextRenderer";
import CourseSubcourseBadges from "../../commonComponents/CourseSubcourseBadges";

const NormalQuestionCard = ({
  question,
  index,
  startIndex,
  statuses,
  loading,
  handleStatusChange,
}) => {
  const cardBgColor =
    question?.approval_status === "approved"
      ? "#e6ffee"
      : question?.approval_status === "rejected"
      ? "#ffe6e6"
      : "#ffffb3";

  return (
    <Card
      className="mb-4 shadow-sm"
      style={{
        backgroundColor: cardBgColor,
        position: "relative",
        overflow: "hidden",
        maxHeight: "80vh",
      }}
    >
      <Card.Body
        style={{
          padding: 0,
          position: "relative",
          height: "100%",
          overflowY: "auto", // Enable vertical scroll
          scrollbarWidth: "none", // Hide scrollbar for Firefox
          msOverflowStyle: "none", // Hide scrollbar for IE/Edge
        }}
        // Hide scrollbar for Webkit browsers
        className="hide-scrollbar"
      >
        {/* Header */}
        <div
          className="d-flex justify-content-between align-items-start mb-3 px-3 pt-3"
          style={{
            position: "sticky",
            top: 0,
            zIndex: 10,
            background: cardBgColor,
            borderBottom: "1px solid rgba(0,0,0,0.1)",
            paddingTop: "8px",
            paddingBottom: "8px",
            boxShadow: "0 2px 8px -6px rgba(0,0,0,0.2)",
          }}
        >
          <div className="flex-grow-1 d-flex align-items-center flex-wrap">
            <h6 className="text-muted mb-2 me-3" style={{ fontSize: "0.85rem" }}>
              Question #{startIndex + index + 1} | ID: {question?.question_id}
              {question?.language && (
                <span className="ms-3">
                  <Badge bg="info" text="dark">
                    Language: {question.language}
                  </Badge>
                </span>
              )}
            </h6>
          </div>
          <ButtonGroup size="sm">
            <Button
              variant={
                question?.approval_status === "rejected"
                  ? "danger"
                  : "outline-danger"
              }
              onClick={() =>
                handleStatusChange?.("rejected", question?.question_id, "question")
              }
              disabled={loading?.[question?.question_id]}
            >
              Rejected
            </Button>
            <Button
              variant={
                question?.approval_status === "pending"
                  ? "secondary"
                  : "outline-secondary"
              }
              onClick={() =>
                handleStatusChange?.("pending", question?.question_id, "question")
              }
              disabled={loading?.[question?.question_id]}
            >
              Pending
            </Button>
            <Button
              variant={
                question?.approval_status === "approved"
                  ? "success"
                  : "outline-success"
              }
              onClick={() =>
                handleStatusChange?.("approved", question?.question_id, "question")
              }
              disabled={loading?.[question?.question_id]}
            >
              Approved
            </Button>
          </ButtonGroup>
        </div>

        {/* Content */}
        <div className="px-3 pb-3" style={{ paddingTop: 0 }}>
          {/* First Line: Difficulty + Course */}
          <div className="d-flex flex-wrap align-items-start gap-2 mb-2">
            {question?.difficulty && (
              <Badge bg="warning" text="dark">
                Difficulty: {question.difficulty}/5
              </Badge>
            )}
            {question?.course && (
              <CourseSubcourseBadges courses={question?.course} />
            )}
          </div>

          {/* Second Line: Subject + Subcourse */}
          <div className="d-flex flex-wrap align-items-center gap-2 mb-2">
            {question?.subject_name && (
              <CourseSubcourseBadges subjects={question?.subject_name} />
            )}
            {question?.subcourse && (
              <CourseSubcourseBadges subcourses={question?.subcourse} />
            )}
          </div>

          {/* Third Line: Topic */}
          <div className="d-flex flex-wrap align-items-center gap-2 mb-3">
            {question?.topic_name && (
              <CourseSubcourseBadges topics={question?.topic_name} />
            )}
          </div>

          {/* Previous Year */}
          {question?.previous_year_questions &&
            question.previous_year_questions.length > 0 && (
              <div className="mb-3">
                <Badge bg="danger" className="text-wrap">
                  <FaClipboardList className="me-1" />
                  Previous Year Question
                </Badge>
                <div className="mt-2">
                  {question.previous_year_questions.map((pyq, idx) => (
                    <div key={idx} className="small text-muted">
                      <FaCalendarAlt className="me-1" />
                      {pyq.course?.name} - {pyq.exams?.name} ({pyq.year}, Month:{" "}
                      {pyq.month})
                      {pyq.note && (
                        <span className="ms-2 fst-italic">Note: {pyq.note}</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

          {/* Question Content */}
          <Card.Text
            className="mb-3"
            style={{ fontSize: "1.1rem", textAlign: "justify" }}
          >
            <FaQuestionCircle className="me-2 text-primary" />
            <MathTextRenderer text={question?.content} />
          </Card.Text>

          {/* Attachment */}
          {question?.attachments && (
            <div className="question-image mb-3">
              <img
                src={`${import.meta.env.VITE_BASE_URL}/${question.attachments}`}
                alt={`Attachment for question ${question?.question_id}`}
                className="img-fluid rounded-3 shadow-sm"
                style={{
                  maxWidth: "100%",
                  height: "auto",
                  border: "1px solid #e9ecef",
                }}
              />
            </div>
          )}

          {/* Options */}
          {question?.options && question.options.length > 0 ? (
            <div className="mt-3">
              <h6 className="mb-3">Options:</h6>
              <ol style={{ listStyleType: "decimal", padding: "0rem 2rem" }}>
                {question.options.map((option) => (
                  <li
                    key={option.option_id}
                    style={{
                      color: option?.is_correct ? "#198754" : "#dc3545",
                      padding: "8px",
                      borderRadius: "4px",
                      fontSize: "1rem",
                      margin: "8px 0",
                      display: "flex",
                      alignItems: "flex-start",
                      gap: "8px",
                    }}
                  >
                    <FaRegCircle style={{ marginTop: "4px", flexShrink: 0 }} />
                    <div style={{ flex: 1, minWidth: 0 }}>
                      <div className="option-text">
                        <MathTextRenderer text={option?.option_text} />
                        {option?.is_correct && (
                          <Badge bg="success" className="ms-2">
                            Correct
                          </Badge>
                        )}
                      </div>
                      {option?.attachments && (
                        <div className="option-image mt-3">
                          <img
                            src={`${import.meta.env.VITE_BASE_URL}/${option.attachments}`}
                            alt="Option attachment"
                            className="img-fluid rounded-3 shadow-sm"
                            style={{
                              maxWidth: "250px",
                              height: "auto",
                              border: "1px solid #e9ecef",
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </li>
                ))}
              </ol>
            </div>
          ) : (
            <div className="no-options text-center py-3 mt-3">
              <p className="text-muted mb-2">
                No options available for this question.
              </p>
            </div>
          )}

          {/* Explanation */}
          {(question?.explanation || question?.explanation_attachment) && (
            <Accordion className="mt-3">
              <Accordion.Item eventKey="0">
                <Accordion.Header>
                  <FaInfoCircle className="me-2" />
                  Explanation
                </Accordion.Header>
                <Accordion.Body>
                  {question?.explanation && (
                    <div className="explanation-text mb-3">
                      <MathTextRenderer text={question.explanation} />
                    </div>
                  )}
                  {question?.explanation_attachment && (
                    <div className="explanation-image">
                      <img
                        src={`${import.meta.env.VITE_BASE_URL}/${question.explanation_attachment}`}
                        alt="Explanation attachment"
                        className="img-fluid rounded-3 shadow-sm"
                        style={{
                          maxWidth: "100%",
                          height: "auto",
                          border: "1px solid #e9ecef",
                        }}
                      />
                    </div>
                  )}
                </Accordion.Body>
              </Accordion.Item>
            </Accordion>
          )}

          {/* Footer Info */}
          <div className="mt-3 pt-3 border-top">
            <div className="row">
              <div className="col-md-6">
                <small className="text-muted">
                  <strong>Created:</strong>{" "}
                  {new Date(question?.created_at).toLocaleDateString()}
                </small>
              </div>
              <div className="col-md-6">
                <small className="text-muted">
                  <strong>Status:</strong> {question?.status} |{" "}
                  <strong>Approval:</strong> {question?.approval_status}
                </small>
              </div>
            </div>
            {question?.times_attempted > 0 && (
              <div className="mt-2">
                <small className="text-muted">
                  <strong>Attempted:</strong> {question.times_attempted} times |{" "}
                  <strong>Avg Score:</strong>{" "}
                  {question.average_score?.toFixed(1)}%
                </small>
              </div>
            )}
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

export default NormalQuestionCard;
