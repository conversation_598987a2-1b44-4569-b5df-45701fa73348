import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage'; // Use local storage for persistence

// Import reducers
import customerCareReducer from './slice/customerCareSlice';
import subjectReducer from './slice/subjectSlice';
import blogReducer from './slice/blogSlice';
import packageReducer from './slice/packageSlice';
import signUpContentReducer from './slice/signupContentSlice';
import studentErrorLogsReducer from './slice/studentErrorLogSlice';
import bannerReducer from "./slice/bannerSlice"; 
import menuReducer from "./slice/menuSlice";
import modalCountReducer from "./slice/modalCountSlice";
import couponReducer from "./slice/couponSlice";
import rewardReducer from "./slice/rewardSlice";
import eventsReducer from "./slice/eventsSlice";
import popUpReducer from "./slice/popUpSlice";
import walkAroundReducer from "./slice/walkAroundSlice";

// Persist config for redux-persist
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['customerCare'], // Persist only these slices
};

// Combine reducers
const appReducer = combineReducers({
  customerCare: customerCareReducer,
  subject: subjectReducer,
  blogs: blogReducer,
  packages: packageReducer,
  signupContents: signUpContentReducer,
  studentErrorLogs:studentErrorLogsReducer,
  banners: bannerReducer, 
  walkarounds: walkAroundReducer,
  menus: menuReducer,
  modalCounts: modalCountReducer,
  coupons: couponReducer,
  rewards: rewardReducer,
  events: eventsReducer,
  popups: popUpReducer,
});

// Root reducer to reset specific slices and non-persisted state
const rootReducer = (state, action) => {
  if (action.type === 'LOGOUT_CONTRIBUTOR') {
    return {
      ...appReducer(undefined, action), // Clears non-persisted state
      customerCare: state.customerCare, // Preserve customerCare state
    };
  }

  if (action.type === 'LOGOUT_CUSTOMER_CARE') {
    return {
      ...appReducer(undefined, action), // Clears non-persisted state
      contributor: state.contributor, // Preserve contributor state
    };
  }

  return appReducer(state, action);
};

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Create the Redux store
export const store = configureStore({
  reducer: persistedReducer,
  devTools: import.meta.env.VITE_REDUX_DEVTOOLS === 'true', // Enable DevTools based on .env
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false, // Disables serializability checks
    }),
});

// Create persistor
export const persistor = persistStore(store);

export default store;
