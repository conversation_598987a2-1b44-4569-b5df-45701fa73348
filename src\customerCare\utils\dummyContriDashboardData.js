const data = {
  contributor: "nayan",
  questions_summary: {
    questions: {
      total: {
        created: 15,
        approved: 10,
        pending: 3,
        rejected: 2,
      },
    },
    master_questions: {
      total: {
        created: 5,
        approved: 3,
        pending: 1,
        rejected: 1,
      },
    },
    master_options: {
      total: {
        created: 8,
        approved: 6,
        pending: 2,
        rejected: 0,
      },
    },
    blogs: {
      total: {
        created: 12,
        approved: 9,
        pending: 3,
        rejected: 0,
      },
    },
    previous_questions: {
      total: {
        created: 2,
        approved: 1,
        pending: 1,
        rejected: 0,
      },
    },
  },
  current_month_data: {
    questions: {
      daily: {
        created: 2,
        approved: 1,
        pending: 1,
        rejected: 0,
      },
      weekly: {
        created: 4,
        approved: 3,
        pending: 1,
        rejected: 0,
      },
      monthly: {
        created: 9,
        approved: 6,
        pending: 2,
        rejected: 1,
      },
    },
    master_questions: {
      daily: {
        created: 1,
        approved: 0,
        pending: 1,
        rejected: 0,
      },
      weekly: {
        created: 2,
        approved: 1,
        pending: 0,
        rejected: 1,
      },
      monthly: {
        created: 3,
        approved: 2,
        pending: 0,
        rejected: 1,
      },
    },
    blogs: {
      daily: {
        created: 1,
        approved: 1,
        pending: 0,
        rejected: 0,
      },
      weekly: {
        created: 3,
        approved: 2,
        pending: 1,
        rejected: 0,
      },
      monthly: {
        created: 8,
        approved: 6,
        pending: 2,
        rejected: 0,
      },
    },
    master_options: {
      daily: {
        created: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
      },
      weekly: {
        created: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
      },
      monthly: {
        created: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
      },
    },
    previous_questions: {
      daily: {
        created: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
      },
      weekly: {
        created: 1,
        approved: 1,
        pending: 0,
        rejected: 0,
      },
      monthly: {
        created: 1,
        approved: 1,
        pending: 0,
        rejected: 0,
      },
    },
  },
  previous_month_data: {
    questions: {
      monthly: {
        created: 5,
        approved: 4,
        pending: 1,
        rejected: 0,
      },
    },
    master_questions: {
      monthly: {
        created: 2,
        approved: 1,
        pending: 1,
        rejected: 0,
      },
    },
    blogs: {
      monthly: {
        created: 4,
        approved: 3,
        pending: 1,
        rejected: 0,
      },
    },
    master_options: {
      monthly: {
        created: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
      },
    },
    previous_questions: {
      monthly: {
        created: 1,
        approved: 0,
        pending: 1,
        rejected: 0,
      },
    },
  },
  third_month_data: {
    questions: {
      monthly: {
        created: 3,
        approved: 2,
        pending: 1,
        rejected: 0,
      },
    },
    master_questions: {
      monthly: {
        created: 1,
        approved: 0,
        pending: 1,
        rejected: 0,
      },
    },
    blogs: {
      monthly: {
        created: 2,
        approved: 2,
        pending: 0,
        rejected: 0,
      },
    },
    master_options: {
      monthly: {
        created: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
      },
    },
    previous_questions: {
      monthly: {
        created: 0,
        approved: 0,
        pending: 0,
        rejected: 0,
      },
    },
  },
  current_month_points: {
    normal_questions: 5,
    master_questions: 10,
    master_options: 0,
    blogs: 20,
    previous_questions: 20,
    total_points: 55,
  },
  previous_month_points: {
    normal_questions: 2,
    master_questions: 5,
    master_options: 0,
    blogs: 50,
    previous_questions: 10,
    total_points: 67,
  },
  third_month_points: {
    normal_questions: 0,
    master_questions: 1,
    master_options: 0,
    blogs: 30,
    previous_questions: 5,
    total_points: 36,
  },
};

export default data;
