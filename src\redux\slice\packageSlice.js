import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get authToken from Redux state
const getAuthToken = (getState) => {
  const { access } = getState().customerCare; // Adjust state path as needed
  return access;
};

// Create a package
export const createPackage = createAsyncThunk(
  "packages/create",
  async ({ formData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PACKAGE}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to create package");
    }
  }
);

// Get all packages
export const getPackages = createAsyncThunk(
  "packages/getAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PACKAGE}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch packages");
    }
  }
);

// Edit a package
export const editPackage = createAsyncThunk(
  "packages/edit",
  async ({ packageId, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PACKAGE_DETAILS}${packageId}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to update package");
    }
  }
);

// Delete a package
export const deletePackage = createAsyncThunk(
  "packages/delete",
  async (packageId, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(`${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PACKAGE_DETAILS}${packageId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return packageId;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to delete package");
    }
  }
);

// Subscribe to a package
export const subscribeToPackage = createAsyncThunk(
  "packages/subscribe",
  async (packageId, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PACKAGE}/subscribe/${packageId}`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to subscribe to package");
    }
  }
);

// See current subscription
export const seeSubscription = createAsyncThunk(
  "packages/seeSubscription",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PACKAGE}/subscription`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch subscription");
    }
  }
);

// Package slice
const packageSlice = createSlice({
  name: "packages",
  initialState: {
    packages: [],
    subscription: null,
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get all packages
      .addCase(getPackages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getPackages.fulfilled, (state, action) => {
        state.loading = false;
        state.packages = action.payload;
      })
      .addCase(getPackages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Create a package
      .addCase(createPackage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPackage.fulfilled, (state, action) => {
        state.loading = false;
        state.packages.push(action.payload);
      })
      .addCase(createPackage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Edit a package
      .addCase(editPackage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(editPackage.fulfilled, (state, action) => {
        state.loading = false;
        state.packages = state.packages.map((pkg) =>
          pkg.id === action.payload.id ? action.payload : pkg
        );
      })
      .addCase(editPackage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete a package
      .addCase(deletePackage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deletePackage.fulfilled, (state, action) => {
        state.loading = false;
        state.packages = state.packages.filter((pkg) => pkg.id !== action.payload);
      })
      .addCase(deletePackage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Subscribe to a package
      .addCase(subscribeToPackage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(subscribeToPackage.fulfilled, (state, action) => {
        state.loading = false;
        state.subscription = action.payload;
      })
      .addCase(subscribeToPackage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // See current subscription
      .addCase(seeSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(seeSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.subscription = action.payload;
      })
      .addCase(seeSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default packageSlice.reducer;
