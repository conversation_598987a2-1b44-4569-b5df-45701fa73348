import React, { useState, useMemo, useEffect } from "react";
import { useLocation } from 'react-router-dom';
import { Card, Form, Button, Modal, ButtonGroup, Dropdown, DropdownButton, Pagination } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { patchCustomerCareQuestions } from "../../redux/slice/customerCareSlice";
import NormalQuestionCard from "./NormalQuestionCard";
import MasterQuestionCard from "./MasterQuestionCard";
import MasterOptionCard from "./MasterOptionCard";
import BlogCard from "./BlogCard";
import RejectQuestionModal from "./RejectQuestionModal";
import toast from "react-hot-toast";
import Swal from "sweetalert2";
import Skeleton from "react-loading-skeleton";

const ContributorQuestionCard = ({ allQuestions, refetchQuestions, subjectName, questionLoading }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const questionType = location.pathname.split("/").pop()
  // console.log("QUESTION TYPE", questionType)
  const [statuses, setStatuses] = useState({});
  const [previousStatus, setPreviousStatus] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [attachment, setAttachment] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [questionsPerPage, setQuestionsPerPage] = useState(5);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState({});
  const [selectedQuestionId, setSelectedQuestionId] = useState(null);

  const questions = allQuestions?.[questionType];

  // Set initial statuses when questions are loaded
  useEffect(() => {
    if (questions) {
      const initialStatuses = {};

      if (questionType === "MasterQuestions") {
        questions?.forEach((masterQ) => {
          masterQ.questions.forEach((question) => {
            initialStatuses[question.question_id] = question.approval_status;
          });
        });
      } else if (questionType === "MasterOptions") {
        questions?.forEach((option) => {
          option.related_questions.forEach((question) => {
            initialStatuses[question.question_id] = question.approval_status;
          });
        });
      } else if (questionType === "Blogs") {
        questions?.forEach((blog) => {
          initialStatuses[blog.id] = blog.approval_status; // Use `id` for blogs
        });
      } else {
        questions?.forEach((question) => {
          console.log("Normla question : ", questions);

          initialStatuses[question.question_id] = question.approval_status;
        });
      }

      setStatuses(initialStatuses);
    }
  }, [questions, questionType]);


  // Runs whenever customerCareQuestions updates

  // Filter questions based on search query
  const filteredQuestions = useMemo(() => {
    if (questionType === "MasterQuestions") {
      return questions?.filter(
        (masterQ) =>
          masterQ?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          masterQ?.passage_content?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    } else if (questionType === "MasterOptions") {
      return questions?.filter(
        (option) =>
          option?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.related_questions.some((q) =>
            q?.content?.toLowerCase().includes(searchQuery.toLowerCase())
          )
      );
    } else if (questionType === "Blogs") {
      return questions?.filter(
        (blog) =>
          blog?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          blog?.introduction?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    } else {
      return questions?.filter((question) =>
        question?.content?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
  }, [searchQuery, questions, questionType]);




  // Pagination logic
  const totalPages = Math.ceil(filteredQuestions?.length / questionsPerPage);
  const startIndex = (currentPage - 1) * questionsPerPage;
  const currentQuestions = filteredQuestions?.slice(startIndex, startIndex + questionsPerPage);

  // console.log("CURRENT QUESTIONS", currentQuestions);


  const handleStatusChange = (newStatus, itemId, entityType) => {
    return new Promise((resolve, reject) => {
      if (loading[itemId]) {
        reject("Operation in progress");
        return;
      }

      // If status is being set to rejected, show the modal instead
      if (newStatus === "rejected") {
        setPreviousStatus({ id: itemId, status: statuses[itemId], entityType });
        setSelectedQuestionId(itemId);
        setShowModal(true);
        return reject("Showing rejection modal");
      }

      // Check if already approved or pending
      if (
        (newStatus === "approved" && statuses[itemId] === "approved") ||
        (newStatus === "pending" && statuses[itemId] === "pending")
      ) {
        Swal.fire({
          icon: "info",
          title:
            newStatus === "approved"
              ? "This question is already approved."
              : "This question is already marked as pending.",
          confirmButtonText: "OK",
        });
        return reject("Already in desired status");
      }

      // SweetAlert confirmation for approve/pending
      let confirmText =
        newStatus === "approved"
          ? "Do you want to approve this question?"
          : "Do you want to mark this question as pending?";

      Swal.fire({
        title: confirmText,
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      }).then((result) => {
        if (!result.isConfirmed) {
          reject("Action cancelled");
          return;
        }

        setLoading((prevLoading) => ({ ...prevLoading, [itemId]: true }));

        const patchData = {
          entity_type: entityType,
          id: itemId,
          approval_status: newStatus,
        };

        // Get the question object from current questions
        const findQuestion = (questions) => {
          if (!questions) return null;
          if (entityType === "master_question") {
            return questions.find(q => q.master_question_id === itemId);
          } else if (entityType === "master_option") {
            return questions.find(q => q.master_option_id === itemId);
          } else if (entityType === "question") {
            if (questionType === "MasterQuestions") {
              for (const masterQ of questions) {
                const question = masterQ.questions.find(q => q.question_id === itemId);
                if (question) return question;
              }
            } else if (questionType === "MasterOptions") {
              for (const option of questions) {
                const question = option.related_questions.find(q => q.question_id === itemId);
                if (question) return question;
              }
            } else {
              return questions.find(q => q.question_id === itemId);
            }
          }
          return null;
        };

        dispatch(patchCustomerCareQuestions({ questionData: patchData }))
          .then(({ meta, payload }) => {
            if (meta.requestStatus === "fulfilled") {
              refetchQuestions(subjectName);
              resolve(payload);
            } else {
              reject(payload?.message || "Update failed");
            }
          })
          .catch(reject)
          .finally(() => {
            setLoading((prevLoading) => ({ ...prevLoading, [itemId]: false }));
          });
      });
    });
  };



  const handleModalClose = () => {
    setShowModal(false);
    setStatuses((prevStatuses) => ({
      ...prevStatuses,
      [previousStatus?.id]: previousStatus?.status,
    }));
  };

  const handleRejectionSubmit = (rejectionReason, attachment, entityType, itemId) => {
    // console.log("handleRejectionSubmit triggered");
    // console.log("Item ID:", itemId);
    // console.log("Rejection Reason:", rejectionReason);
    // console.log("Attachment:", attachment);
    // console.log("Entity Type:", entityType);

    // Ensure rejectionReason is always a string
    rejectionReason = String(rejectionReason || ""); // Convert to string, default to empty string if falsy

    // Validate the rejection reason
    if (!rejectionReason.trim()) {
      toast.error("Please provide a rejection reason.");
      return;
    }

    // Close the modal after validation (before starting the submission process)
    setShowModal(false);

    const prevStatus = statuses[itemId]; // Store the previous status

    // Optimistically update the status to "rejected"
    setStatuses((prevStatuses) => ({
      ...prevStatuses,
      [itemId]: "rejected",
    }));

    // Set loading state to true for this item
    setLoading((prevLoading) => ({ ...prevLoading, [itemId]: true }));

    // Construct the data based on the passed entityType (e.g., blog, master_question, master_option, or question)
    let itemData = {
      entity_type: entityType, // Use the entityType passed from the component
      id: itemId,
      approval_status: "rejected",
      reason: rejectionReason,
    };

    // If an attachment exists, handle it as FormData
    if (attachment) {
      const formData = new FormData();
      Object.entries(itemData).forEach(([key, value]) => formData.append(key, value));
      formData.append("reason_document", attachment);
      itemData = formData;
    }

    // Dispatch the action to update the rejection status
    dispatch(patchCustomerCareQuestions({ questionData: itemData }))
      .then(({ meta, payload }) => {
        if (meta.requestStatus === "fulfilled") {
          toast.success(payload?.message || "Item rejected successfully!");
          refetchQuestions(subjectName);
        } else {
          throw new Error(payload?.message || "Update failed");
        }
      })
      .catch((err) => {
        toast.error("Error rejecting item: " + (err?.message || "Unknown error"));

        // Restore the previous status in case of failure
        setStatuses((prevStatuses) => ({
          ...prevStatuses,
          [itemId]: prevStatus,
        }));
      })
      .finally(() => {
        // Set loading state to false once the process is complete
        setLoading((prevLoading) => ({ ...prevLoading, [itemId]: false }));
      });
  };





  const handleFileChange = (e) => {
    setAttachment(e.target.files[0]);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleQuestionsPerPageChange = (num) => {
    setQuestionsPerPage(num);
    setCurrentPage(1);
  };

  const renderPagination = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;
    const start = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    const end = Math.min(totalPages, start + maxPagesToShow - 1);

    for (let i = start; i <= end; i++) {
      pageNumbers.push(i);
    }

    const prevEllipsis = start > 1 ? <Pagination.Ellipsis /> : null;
    const nextEllipsis = end < totalPages ? <Pagination.Ellipsis /> : null;

    return (
      <>
        <Pagination.Prev
          onClick={() => setCurrentPage(currentPage - 1)}
          disabled={currentPage === 1}
        />
        {prevEllipsis}
        {pageNumbers.map((pageNum) => (
          <Pagination.Item
            key={pageNum}
            active={currentPage === pageNum}
            onClick={() => setCurrentPage(pageNum)}
          >
            {pageNum}
          </Pagination.Item>
        ))}
        {nextEllipsis}
        <Pagination.Next
          onClick={() => setCurrentPage(currentPage + 1)}
          disabled={currentPage === totalPages}
        />
      </>
    );
  };

  return (
    <>
      {/* Search Bar */}
      <div className="d-flex justify-content-center mb-3">
        <Form.Control
          type="text"
          placeholder="Search questions..."
          value={searchQuery}
          onChange={handleSearchChange}
          className="w-50 m-1"
        />
        {/* Dropdown for Questions per Page */}
        <DropdownButton
          id="questions-per-page-dropdown"
          title={`Q / page: ${questionsPerPage}`}
          onSelect={handleQuestionsPerPageChange}
          className="m-1"
        >
          {[2, 5, 10, 15, 20, 25].map((num) => (
            <Dropdown.Item key={num} eventKey={num}>
              {num}
            </Dropdown.Item>
          ))}
        </DropdownButton>
      </div>


      {/* Question Cards */}
      {questionLoading ? (
        <div className="">
          <Skeleton height={150} className="mb-3" count={3} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
        </div>
      ) : (
        currentQuestions?.map((question, index) =>
          questionType === "MasterQuestions" ? (
            <MasterQuestionCard
              key={question?.master_question_id}
              masterQuestion={question}
              index={index}
              startIndex={startIndex}
              statuses={statuses}
              loading={loading}
              handleStatusChange={handleStatusChange}
            />
          ) : questionType === "MasterOptions" ? (
            <MasterOptionCard
              key={question?.master_option_id}
              option={question}
              index={index}
              startIndex={startIndex}
              statuses={statuses}
              loading={loading}
              handleStatusChange={handleStatusChange}
              relatedQuestions={question.related_questions}
            />
          ) : questionType === "Blogs" ? (
            <BlogCard
              key={question?.id}
              index={index}
              startIndex={startIndex}
              blog={question}
              statuses={statuses}
              loading={loading}
              handleStatusChange={handleStatusChange}
            />
          ) : (
            <NormalQuestionCard
              key={question?.question_id}
              question={question}
              index={index}
              startIndex={startIndex}
              statuses={statuses}
              loading={loading}
              handleStatusChange={handleStatusChange}
            />
          )
        )
      )}


      {/* Pagination Controls */}
      <Pagination className="d-flex justify-content-center">
        {renderPagination()}
      </Pagination>

      {/* Reject Question Modal */}
      <RejectQuestionModal
        show={showModal}
        onClose={handleModalClose}
        onSubmit={handleRejectionSubmit}
        previousStatus={previousStatus}
        entityType={previousStatus?.entityType}  // Pass the entityType dynamically
        itemId={previousStatus?.id}  // Pass itemId dynamically
      />

    </>
  );
};

export default ContributorQuestionCard;
