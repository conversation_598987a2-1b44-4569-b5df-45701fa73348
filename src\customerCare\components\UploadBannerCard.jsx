import React, { useRef, useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Card } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-hot-toast";
import { createBanner } from "../../redux/slice/bannerSlice";
import imageCompression from "browser-image-compression";

const UploadBannerCard = ({ newBanner, setNewBanner, handleInputChange, handleAddBanner }) => {
  const dispatch = useDispatch();
  const error = useSelector((state) => state.banners.error);
  const [image, setImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [preview, setPreview] = useState(null);
  const [isCheckingImage, setIsCheckingImage] = useState(false);
  const [imageSizeText, setImageSizeText] = useState("");

  const imageInputRef = useRef(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    setIsCheckingImage(true);
    setImageError("");
    setImage(null);
    setPreview(null);

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 250 * 1024) {
      // Directly set the image if it is small enough
      setImage(file);
      setNewBanner((prev) => ({ ...prev, banner_image: file }));
      const reader = new FileReader();
      reader.onload = () => setPreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingImage(false);
    } else {
      const options = {
        maxSizeMB: 0.25,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options)
          .then((compressedFile) => {
            const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);
            console.log("Compressed file size:", compressedFile.size);

            if (compressedFile.size <= 250 * 1024) {
              // Convert Blob to File (required for form data)
              const fileName = "compressed_" + file.name;
              const compressedFileAsFile = new File(
                [compressedFile],
                fileName,
                {
                  type: compressedFile.type,
                }
              );

              console.log("Setting compressed image:", compressedFileAsFile);

              // Set the image as File object
              setImage(compressedFileAsFile);
              setNewBanner((prev) => ({ ...prev, banner_image: compressedFileAsFile }));

              const reader = new FileReader();
              reader.onload = () => setPreview(reader.result);
              reader.readAsDataURL(compressedFileAsFile);

              // Display the image sizes in the UI
              setImageSizeText(
                `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
              );
            } else {
              setImageError(
                `Image exceeds 250KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`
              );
            }
          })
          .catch((error) => {
            console.error("Image compression failed:", error);
            setImageError("An error occurred while compressing the image.");
          })
          .finally(() => {
            setIsCheckingImage(false);
          });
      } catch (error) {
        console.error("Error handling image change:", error);
        setImageError("An error occurred while processing the image.");
        setIsCheckingImage(false);
      }
    }
  };

  // Function to reset image preview after successful upload
  const resetImagePreview = () => {
    setImage(null);
    setPreview(null);
    setImageSizeText("");
    setImageError("");

    if (imageInputRef.current) {
      imageInputRef.current.value = ""; // Reset file input field
    }
  };

  return (
    <Card className="mb-4 shadow">
      <Card.Body>
        <Form onSubmit={(e) => handleAddBanner(e, resetImagePreview)}>
          <Form.Group controlId="bannerName">
            <Form.Label>Banner Name</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter banner name"
              name="banner_name"
              value={newBanner.banner_name}
              onChange={handleInputChange}
              required
            />
          </Form.Group>

          <Form.Group controlId="bannerImage" className="my-3">
            {imageSizeText && <p className="text-success">{imageSizeText}</p>}
            {imageError && <p className="text-danger mb-2">{imageError}</p>}
            <Form.Label>Banner Image (Under 250 KB)</Form.Label>
            <Form.Control
              ref={imageInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageChange}
            />
          </Form.Group>

          {preview && (
            <div className="mb-3">
              <img
                src={preview}
                alt="Preview"
                style={{
                  width: "100%",
                  maxHeight: "200px",
                  objectFit: "cover",
                }}
              />
            </div>
          )}

          <Button variant="outline-success w-100 mt-3" type="submit">
            Add Banner
          </Button>

          {/* {error && <p className="text-danger mt-2">Error: {error}</p>} */}
        </Form>
      </Card.Body>
    </Card>
  );
};

export default UploadBannerCard;
