import React from "react";
import { Card, Button, ButtonGroup } from "react-bootstrap";
import { Link } from "react-router-dom";
import { useDispatch } from "react-redux";
import { sendNotification } from "../../redux/slice/notificationSlice";
import NormalQuestionCard from './NormalQuestionCard';
import MasterOptionCard from './MasterOptionCard';
import MasterQuestionCard from './MasterQuestionCard';
import MathTextRenderer from "../../commonComponents/MathTextRenderer";

const BlogCard = ({ blog, index, startIndex, statuses, loading, handleStatusChange }) => {
    const dispatch = useDispatch();

    const handleApproval = () => {
        handleStatusChange("approved", blog?.id, "blog")
            .then(() => {
                console.log("Blog approved, sending notification");
                return dispatch(sendNotification({
                    notification: {
                        title: blog?.title || "",
                        body: blog?.short_content || "",
                        image: blog?.image ? `${import.meta.env.VITE_BASE_URL}/${blog?.image}` : "",
                        description: ".",
                    },
                    data: {
                        title: blog?.title || "",
                        body: blog?.short_content || "",
                        image: blog?.image ? `${import.meta.env.VITE_BASE_URL}/${blog?.image}` : "",
                        description: ".",
                    },
                }));
            })
            .catch(error => console.error("Error:", error));
    };

    return (
        <Card className="mb-4 w-100 shadow-sm">
            {/* Status Button Group for Blog */}
            <div className="d-flex justify-content-end m-2">
                <ButtonGroup>
                    <Button
                        variant={statuses?.[blog?.id] === "rejected" ? "danger" : "outline-danger"}
                        onClick={() => handleStatusChange("rejected", blog?.id, "blog")}
                        size="sm"
                        disabled={loading?.[blog?.id]}
                    >
                        Rejected
                    </Button>
                    <Button
                        variant={statuses?.[blog?.id] === "pending" ? "secondary" : "outline-secondary"}
                        onClick={() => handleStatusChange("pending", blog?.id, "blog")}
                        size="sm"
                        disabled={loading?.[blog?.id]}
                    >
                        Pending
                    </Button>
                    <Button
                        variant={statuses?.[blog?.id] === "approved" ? "success" : "outline-success"}
                        onClick={handleApproval}
                        size="sm"
                        disabled={loading?.[blog?.id]}
                    >
                        Approved
                    </Button>
                </ButtonGroup>
            </div>
            {/* View Buttons */}
            <div className="d-flex justify-content-end me-1">
                <a
                    href={`${import.meta.env.VITE_SHASHTRATH_BLOG}blog/${blog?.slug}`}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <Button variant="outline-dark" className="mx-1" style={{ fontSize: "0.7rem" }}>
                        Public View
                    </Button>
                </a>
                <Link to={`/blog/${blog?.slug}`}>
                    <Button variant="outline-info" className="mx-1" style={{ fontSize: "0.7rem" }}>
                        Internal View
                    </Button>
                </Link>
            </div>
            <Card.Img
                variant="top"
                src={`${import.meta.env.VITE_BASE_URL}/${blog?.image}`}
                alt={blog?.image_caption || "Blog Image"}
                className="img-fluid"
                style={{
                    maxHeight: "200px",
                    maxWidth: "100%",
                    objectFit: "contain",
                }}
            />
            <Card.Body>
                <Card.Title>
                    {startIndex + index + 1}. <MathTextRenderer text={blog?.title} />
                </Card.Title>
                <Card.Subtitle className="my-2">
                    <small className="text-muted">Published on: {new Date(blog?.published_date)?.toLocaleDateString()}</small> <br />
                    <small>By Author: {blog?.author_first_name} {blog?.author_last_name}</small>
                </Card.Subtitle>
                <Card.Text className="text-truncate-4">
                    <MathTextRenderer text={blog?.short_content} />
                </Card.Text>



                {/* Render Normal Question Cards for Related Questions */}
                {blog?.normal_questions?.length > 0 && (
                    <div className="mt-3">
                        <h3 className="text-success">Related Normal Questions</h3>
                        {blog?.normal_questions?.map((question, index) => (
                            <div key={question?.question_id} className="d-flex flex-column p-2 rounded mb-2">
                                <NormalQuestionCard
                                    question={question}
                                    index={index}
                                    startIndex={startIndex}
                                    statuses={statuses}
                                    loading={loading}
                                    handleStatusChange={handleStatusChange}
                                />
                            </div>
                        ))}
                    </div>
                )}

                {/* Render Master Question Cards for Related Master Questions */}
                {blog?.master_questions?.length > 0 && (
                    <div className="mt-3">
                        <h3 className="text-success">Related Master Questions</h3>
                        {blog?.master_questions?.map((masterQuestion, index) => (
                            <div key={masterQuestion?.question_id} className="d-flex flex-column p-2 rounded mb-2">
                                <MasterQuestionCard
                                    masterQuestion={masterQuestion}
                                    index={index}
                                    startIndex={startIndex}
                                    statuses={statuses}
                                    loading={loading}
                                    handleStatusChange={handleStatusChange}
                                />
                            </div>
                        ))}
                    </div>
                )}

                {/* Render Master Option Cards for Related Master Options */}
                {blog?.master_options?.length > 0 && (
                    <div className="mt-3">
                        <h3 className="text-success">Related Master Options</h3>
                        {blog?.master_options?.map((masterOption, index) => (
                            <div key={masterOption?.option_id} className="d-flex flex-column p-2 rounded mb-2">
                                <MasterOptionCard
                                    option={masterOption}
                                    index={index}
                                    startIndex={startIndex}
                                    statuses={statuses}
                                    loading={loading}
                                    handleStatusChange={handleStatusChange}
                                />
                            </div>
                        ))}
                    </div>
                )}
            </Card.Body>
        </Card>
    );
};

export default BlogCard;

