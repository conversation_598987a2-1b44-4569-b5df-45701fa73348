import React, { useState, useCallback } from 'react';
import { Card, Form, Button, Row, Col, Alert } from 'react-bootstrap';
import MathInput from './MathInput';
import MathPreview from './MathPreview';
import './MathEditor.css';

const MathEditor = ({
  value = '',
  onChange,
  label = 'Mathematical Expression',
  placeholder = 'Enter mathematical expression...',
  showPreview = true,
  showRawLatex = false,
  displayMode = false,
  disabled = false,
  showSaveButton = false,
  onSave,
  className = '',
  // New props for embedded mode
  embeddedMode = false,
  textContent = '',
  onTextContentChange
}) => {
  const [latex, setLatex] = useState(value);
  const [showAlert, setShowAlert] = useState(false);

  const handleLatexChange = useCallback((newLatex) => {
    setLatex(newLatex);
    if (onChange) {
      onChange(newLatex);
    }
  }, [onChange]);

  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(latex);
    }
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  }, [latex, onSave]);

  const handleInsertMath = useCallback(() => {
    if (embeddedMode && onTextContentChange && latex.trim()) {
      const mathExpression = displayMode ? `$$${latex}$$` : `$${latex}$`;
      const newContent = textContent + ' ' + mathExpression;
      onTextContentChange(newContent);
      setLatex(''); // Clear the math input after insertion
      setShowAlert(true);
      setTimeout(() => setShowAlert(false), 3000);
    }
  }, [embeddedMode, onTextContentChange, latex, textContent, displayMode]);

  return (
    <div className={className}>
      <Card>
        <Card.Body>
          {showAlert && (
            <Alert variant="success" className="mb-3">
              {embeddedMode ? 'Math expression inserted!' : 'Math expression saved!'}
            </Alert>
          )}

          {/* Math Input */}
          <Form.Group className="mb-3">
            <Form.Label>{label}</Form.Label>
            <MathInput
              value={latex}
              onChange={handleLatexChange}
              placeholder={placeholder}
              disabled={disabled}
              showVirtualKeyboard={true}
            />
          </Form.Group>

          {/* Action Buttons */}
          <Row className="mb-3">
            <Col>
              {embeddedMode && (
                <Button
                  variant="primary"
                  onClick={handleInsertMath}
                  disabled={!latex.trim() || disabled}
                  className="me-2"
                >
                  Insert Math Expression
                </Button>
              )}
              {showSaveButton && (
                <Button
                  variant="success"
                  onClick={handleSave}
                  disabled={!latex.trim() || disabled}
                >
                  Save Expression
                </Button>
              )}
              <Button
                variant="outline-secondary"
                onClick={() => setLatex('')}
                disabled={!latex.trim() || disabled}
                className="ms-2"
              >
                Clear
              </Button>
            </Col>
          </Row>

          {/* Math Preview */}
          {showPreview && (
            <Form.Group className="mb-3">
              <Form.Label>Preview</Form.Label>
              <Card className="bg-light" style={{ minHeight: '80px' }}>
                <Card.Body className="d-flex align-items-center justify-content-center">
                  <MathPreview
                    latex={latex}
                    displayMode={displayMode}
                    showRaw={showRawLatex}
                    placeholder="Math preview will appear here..."
                  />
                </Card.Body>
              </Card>
            </Form.Group>
          )}
          
          {/* Raw LaTeX Display */}
          {showRawLatex && latex && (
            <Form.Group className="mb-3">
              <Form.Label>Generated LaTeX</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                value={latex}
                readOnly
                className="font-monospace"
                style={{ fontSize: '0.875rem' }}
              />
            </Form.Group>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

export default MathEditor;
