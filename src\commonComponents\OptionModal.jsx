import React from 'react';
import { Modal, Form, Button } from 'react-bootstrap';

const OptionModal = ({ show, handleClose, optionFormData, handleOptionInputChange, handleAddOption, isSubmitting }) => {
  return (
    <Modal
      show={show}
      onHide={handleClose}
      backdrop="static"
      keyboard={false}
      centered
      style={{
        position: 'fixed',
        top: '50%',
        left: '10%',
        transform: 'translateY(-50%)',
        margin: '0',
        width: '310px',
      }}
    >
      <Modal.Header closeButton>
        <Modal.Title>Add Option</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form.Control
          type="text"
          name="optionText"
          value={optionFormData.optionText}
          onChange={handleOptionInputChange}
          placeholder="Enter option text"
        />

        <Form.Check
          type="radio"
          label="Yes"
          name="isCorrect"
          value={true}
          checked={optionFormData.isCorrect === true}
          onChange={handleOptionInputChange}
        />
        <Form.Check
          type="radio"
          label="No"
          name="isCorrect"
          value={false}
          checked={optionFormData.isCorrect === false}
          onChange={handleOptionInputChange}
        />

        <Button variant="success" onClick={handleAddOption} disabled={isSubmitting} className="w-100">
          Add Option
        </Button>
      </Modal.Body>
    </Modal>
  );
};

export default OptionModal;
