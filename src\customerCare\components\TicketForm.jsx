import React, { useState, useEffect, useRef } from "react";
import { Card, Form, Button, Row, Col } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getCustomerCareList } from "../../redux/slice/customerCareSlice";
import { getStudentList } from "../../redux/slice/studentSlice";
import { createTicket } from "../../redux/slice/ticketSlice";
import toast, { Toaster } from "react-hot-toast";
import imageCompression from "browser-image-compression";

const TicketForm = ({ loading, setSearchTerm, fetchTickets }) => {
  const dispatch = useDispatch();

  const [ticketData, setTicketData] = useState({
    customer: "",
    student: "",
    ticket_status: "open",
    priority: "high",
    subject: "",
    description: "",
    resolve_summary: "",
    attachments: null,
    tags: "",
  });

  const [customerCareList, setCustomerCareList] = useState([]);
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [error, setError] = useState(null);
  const [image, setImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [preview, setPreview] = useState(null);
  const [isCheckingImage, setIsCheckingImage] = useState(false);
  const [imageSizeText, setImageSizeText] = useState("");
  const [loadingCreate, setLoadingCreate] = useState(false);

  const imageInputRef = useRef(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    setIsCheckingImage(true);
    setImageError("");
    setImage(null);
    setPreview(null);

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      // Directly set the image if it is small enough
      setImage(file);
      const reader = new FileReader();
      reader.onload = () => setPreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingImage(false);
    } else {
      const options = {
        maxSizeMB: 0.2,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options)
          .then((compressedFile) => {
            const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);
            console.log("Compressed file size:", compressedFile.size); // Debugging

            if (compressedFile.size <= 200 * 1024) {
              // Convert Blob to File (required for form data)
              const fileName = "compressed_" + file.name;
              const compressedFileAsFile = new File(
                [compressedFile],
                fileName,
                {
                  type: compressedFile.type,
                }
              );

              console.log("Setting compressed image:", compressedFileAsFile);

              // Set the image as File object
              setImage(compressedFileAsFile);

              const reader = new FileReader();
              reader.onload = () => setPreview(reader.result);
              reader.readAsDataURL(compressedFileAsFile);

              // Display the image sizes in the UI
              setImageSizeText(
                `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
              );
            } else {
              setImageError(
                `Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`
              );
            }
          })
          .catch((error) => {
            console.error("Image compression failed:", error);
            setImageError("An error occurred while compressing the image.");
          })
          .finally(() => {
            setIsCheckingImage(false);
          });
      } catch (error) {
        console.error("Error handling image change:", error);
        setImageError("An error occurred while processing the image.");
        setIsCheckingImage(false);
      }
    }
  };

  useEffect(() => {
    dispatch(getCustomerCareList())
      .unwrap()
      .then((response) => {
        setCustomerCareList(response.data);
      })
      .catch((err) => {
        setError("Failed to fetch customer care profiles");
      });
  }, [dispatch]);

  useEffect(() => {
    dispatch(getStudentList())
      .unwrap()
      .then((response) => {
        setStudents(response);
        setFilteredStudents(response);
      })
      .catch((err) => {
        setError("Failed to fetch student list");
      });
  }, [dispatch]);

  const handleStudentSearchChange = (e) => {
    const searchTerm = e.target.value.toLowerCase();
    setSearchTerm(searchTerm);

    const filtered = students.filter((student) =>
      student.user.email.toLowerCase().includes(searchTerm)
    );
    setFilteredStudents(filtered);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setTicketData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleCustomerCareChange = (e) => {
    const selectedCareId = e.target.value;

    if (selectedCareId) {
      // Find the customer care object by the selected id
      const selectedCare = customerCareList.find(
        (care) => care.id === Number(selectedCareId)
      );

      if (selectedCare) {
        setTicketData((prevState) => ({
          ...prevState,
          customer: Number(selectedCare.id),
        }));
        console.log(ticketData);
      } else {
        console.error("Selected customer care not found");
      }
    } else {
      console.error("Selected customer care ID is invalid");
    }
  };

  const handleCreateTicket = async (e) => {
    e.preventDefault();
    setError(null);
    console.log(ticketData);

    const formData = {
      customer_id: ticketData.customer,
      student_id: ticketData.student,
      ticket_status: ticketData.ticket_status,
      priority: ticketData.priority,
      subject: ticketData.subject,
      description: ticketData.description,
      resolve_summary: ticketData.resolve_summary || "",
      tags: ticketData.tags,
      ...(image && { attachments: image }),
    };

    try {
      setLoadingCreate(true);
      const response = await dispatch(createTicket({ data: formData }));
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Ticket has been raised!");
        setSearchTerm(ticketData.subject);
        fetchTickets(); // Refetch tickets after adding a new one
        setTicketData({
          ...ticketData,
          subject: "",
          description: "",
          tags: "",
          resolve_summary: "",
          attachments: null,
        });
        setPreview(null);
        setImage(null);
        setImageSizeText("");
      }else if(response?.meta?.requestStatus === "rejected"){
        toast.error("Something went wrong!");
      }
    } catch (err) {
      setError("Failed to create ticket");
      toast.error("Something went wrong!");
    } finally {
      setLoadingCreate(false);
    }
  };

  return (
    <Card className="shadow p-3 mt-5">
      <Card.Body>
        <h4 className="mb-3 text-center text-success">Create Ticket</h4>
        {error && <div className="alert alert-danger">{error}</div>}

        <Form onSubmit={handleCreateTicket}>
          {/* Customer Care and Student Search at the top */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group controlId="formCustomerCare">
                <Form.Label>Customer Care</Form.Label>
                <Form.Control
                  as="select"
                  name="customer"
                  value={ticketData.customer}
                  onChange={(e) => handleCustomerCareChange(e)} // Explicitly pass the event to the handler
                >
                  <option value="">Select Customer Care</option>
                  {customerCareList.map((care) => (
                    <option key={care?.id} value={care?.id}>
                      {care?.user?.first_name} {care?.user?.last_name}
                    </option>
                  ))}
                </Form.Control>
              </Form.Group>
            </Col>

            <Col md={6}>
              <Form.Group controlId="formStudentSearch">
                <Form.Label>Search Student by Email</Form.Label>
                <Form.Control
                  type="email"
                  placeholder="Search by email"
                  onChange={handleStudentSearchChange}
                />
              </Form.Group>
            </Col>
          </Row>

          {/* Student Dropdown */}
          <Form.Group controlId="formStudent" className="mb-3">
            <Form.Label>Student</Form.Label>
            <Form.Control
              as="select"
              name="student"
              value={ticketData.student}
              onChange={handleInputChange}
            >
              {filteredStudents.length === 0 ? (
                <option>No students found</option>
              ) : (
                <>
                  {ticketData.student === "" && (
                    <option value="">Select Student</option>
                  )}{" "}
                  {/* Conditionally render this line */}
                  {filteredStudents.map((student) => (
                    <option key={student.id} value={student.id}>
                      {student.user.first_name} {student.user.last_name}
                    </option>
                  ))}
                </>
              )}
            </Form.Control>
          </Form.Group>

          {/* Subject and Priority in multi-columns */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group controlId="formSubject">
                <Form.Label>Subject</Form.Label>
                <Form.Control
                  type="text"
                  name="subject"
                  value={ticketData.subject}
                  onChange={handleInputChange}
                  placeholder="Enter ticket subject"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group controlId="formPriority">
                <Form.Label>Priority</Form.Label>
                <Form.Control
                  as="select"
                  name="priority"
                  value={ticketData.priority}
                  onChange={handleInputChange}
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </Form.Control>
              </Form.Group>
            </Col>
          </Row>

          {/* Tags and Ticket Status in multi-columns */}
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group controlId="formTags">
                <Form.Label>Tags</Form.Label>
                <Form.Control
                  type="text"
                  name="tags"
                  value={ticketData.tags}
                  onChange={handleInputChange}
                  placeholder="Enter tags"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group controlId="formTicketStatus">
                <Form.Label>Ticket Status</Form.Label>
                <Form.Control
                  as="select"
                  name="ticket_status"
                  value={ticketData.ticket_status}
                  onChange={handleInputChange}
                >
                  <option value="open">Open</option>
                  <option value="in-progress">In Progress</option>
                  <option value="closed">Closed</option>
                </Form.Control>
              </Form.Group>
            </Col>
          </Row>

          {/* Description in single column */}
          <Row className="mb-3">
            <Col md={12}>
              <Form.Group controlId="formDescription">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={ticketData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the issue"
                />
              </Form.Group>
            </Col>
          </Row>

          {/* Resolve Summary in single column */}
          <Row className="mb-3">
            <Col md={12}>
              <Form.Group controlId="formResolveSummary">
                <Form.Label>Resolve Summary</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="resolve_summary"
                  value={ticketData.resolve_summary}
                  onChange={handleInputChange}
                  placeholder="Provide a summary of resolution (optional)"
                />
              </Form.Group>
            </Col>
          </Row>
          <Row className="mb-3">
            <Col md={12}>
              {/* Attachment Field */}
              <Form.Group controlId="issueImage" className="my-3">
                {imageSizeText && (
                  <p className="text-success">{imageSizeText}</p>
                )}

                {imageError && <p className="text-danger mb-2">{imageError}</p>}
                <Form.Label>Attachment (Optional) (Under 200 KB)</Form.Label>
                <Form.Control
                  ref={imageInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                />
              </Form.Group>

              {preview && (
                <div className="mb-3">
                  <img
                    src={preview}
                    alt="Preview"
                    style={{
                      width: "100%",
                      maxHeight: "200px",
                      objectFit: "cover",
                    }}
                  />
                </div>
              )}
            </Col>
          </Row>

          <Button
            variant="outline-success"
            type="submit"
            className="w-100"
            disabled={loadingCreate}
          >
            {loadingCreate ? "Creating..." : "Create Ticket"}
          </Button>
        </Form>
      </Card.Body>
      <Toaster />
    </Card>
  );
};

export default TicketForm;
