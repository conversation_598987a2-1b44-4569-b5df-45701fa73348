import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Container,
  DropdownButton,
  Dropdown,
  Card,
  Form,
} from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchPopUps,
  createPopUp,
  deletePopUp,
  updatePopUp,
} from "../../redux/slice/popUpSlice";
import UploadPopUpCard from "../components/UploadPopUpCard";
import PopUpCard from "../components/PopUpCard";
import NavigationBar from "../../commonComponents/NavigationBar";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import Swal from "sweetalert2";

const PopUpDashboard = () => {
  const dispatch = useDispatch();
  const [allPopUps, setAllPopUps] = useState([]);
  const [filteredPopUps, setFilteredPopUps] = useState([]);
  const [newPopUp, setNewPopUp] = useState({
    popup_title: "",
    popup_content: "",
    popup_image: null,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState(6);
  const [currentPage, setCurrentPage] = useState(1);
  const error = useSelector((state) => state.popups.error);
  const [isLoading, setIsLoading] = useState(false);

  const fetchPopUpsData = async () => {
    try {
      setIsLoading(true);
      const response = await dispatch(fetchPopUps());
      if (response?.payload) {
        const popups = Array.isArray(response.payload) ? response.payload : [];
        setAllPopUps(popups);
        filterPopUps(popups);
      }
    } catch (error) {
      console.error("Error fetching popups:", error);
      toast.error("Failed to load popups");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPopUpsData();
  }, [dispatch]);

  const handleAddPopUp = async (e, resetImagePreview) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("popup_title", newPopUp?.popup_title);
    formData.append("popup_content", newPopUp?.popup_content);
    if (newPopUp?.popup_image) {
      formData.append("popup_image", newPopUp.popup_image);
    }
    try {
      const response = await dispatch(createPopUp(formData));
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Pop-up added successfully!");
        setNewPopUp({ popup_title: "", popup_content: "", popup_image: null });
        resetImagePreview && resetImagePreview();
        fetchPopUpsData();
      } else {
        toast.error("Failed to add pop-up");
      }
    } catch (error) {
      console.error("Error adding pop-up:", error);
      toast.error(error?.message || "Failed to add pop-up. Please try again!");
    }
  };

  const handleDeletePopUp = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });
    if (result?.isConfirmed) {
      try {
        const response = await dispatch(deletePopUp(id));
        if (response?.meta?.requestStatus === "fulfilled") {
          toast.success("Pop-up deleted successfully!");
          fetchPopUpsData();
        } else {
          toast.error("Failed to delete pop-up");
        }
      } catch (error) {
        console.error("Error deleting pop-up:", error);
        toast.error("Failed to delete pop-up");
      }
    }
  };

  const handleUpdatePopUp = async (id, updatedData) => {
    try {
      const formData = new FormData();
      formData.append("popup_title", updatedData?.popup_title);
      formData.append("popup_content", updatedData?.popup_content);
      if (updatedData?.popup_image) {
        formData.append("popup_image", updatedData.popup_image);
      }
      const response = await dispatch(updatePopUp({ id, formData }));
      if (response?.meta?.requestStatus === "fulfilled") {
        toast.success("Pop-up updated successfully!");
        fetchPopUpsData();
      } else {
        toast.error("Failed to update pop-up");
      }
    } catch (error) {
      console.error("Error updating pop-up:", error);
      toast.error("Failed to update pop-up");
    }
  };

  const filterPopUps = (popups) => {
    const filtered = Array.isArray(popups)
      ? popups.filter((popup) =>
          popup?.popup_title?.toLowerCase().includes(searchTerm?.toLowerCase())
        )
      : [];
    setFilteredPopUps(filtered);
  };

  useEffect(() => {
    filterPopUps(allPopUps);
  }, [searchTerm, allPopUps]);

  const paginate = (array, pageSize, pageNumber) => {
    const offset = (pageNumber - 1) * pageSize;
    return array.slice(offset, offset + pageSize);
  };

  const handleDropdownChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const paginatedPopUps = paginate(filteredPopUps, itemsPerPage, currentPage);
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };
  const totalPages = Math.ceil(filteredPopUps.length / itemsPerPage);
  const indexOfLastPopUp = currentPage * itemsPerPage;
  const indexOfFirstPopUp = indexOfLastPopUp - itemsPerPage;
  const currentPopUps = filteredPopUps.slice(indexOfFirstPopUp, indexOfLastPopUp);

  return (
    <>
      <NavigationBar />
      <Container>
        <Row className="mt-5">
          <Col md={4}>
            <UploadPopUpCard
              newPopUp={newPopUp}
              setNewPopUp={setNewPopUp}
              handleInputChange={(e) =>
                setNewPopUp({ ...newPopUp, [e.target.name]: e.target.value })
              }
              handleFileChange={(e) =>
                setNewPopUp({ ...newPopUp, popup_image: e.target.files[0] })
              }
              handleAddPopUp={handleAddPopUp}
            />
            {error && toast.error("Something went wrong!")}
          </Col>
          <Col md={8}>
            <div className="d-flex justify-content-center mb-3">
              <Form.Control
                type="text"
                placeholder="Search by title"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ width: "60%" }}
              />
              <DropdownButton
                id="dropdown-basic-button"
                title={`Pop-ups per page: ${itemsPerPage}`}
                variant="success"
                className="mx-1"
                onSelect={handleDropdownChange}
              >
                {[2, 6, 8, 16, 32, 64, 100].map((number) => (
                  <Dropdown.Item key={number} eventKey={number}>
                    {number}
                  </Dropdown.Item>
                ))}
              </DropdownButton>
            </div>
            {isLoading ? (
              <Row>
                {[...Array(itemsPerPage)].map((_, index) => (
                  <Col key={index} md={6} className="mb-3">
                    <Card className="position-relative rounded-3 shadow mb-3">
                      <Skeleton
                        height={200}
                        baseColor="#e6ffe6"
                        highlightColor="#c4f7c4"
                        className="rounded-3"
                      />
                      <Card.Body
                        className="position-absolute top-0 start-0 end-0 bottom-0 d-flex justify-content-center align-items-center p-3 text-white rounded-3"
                        style={{
                          backgroundColor: "rgba(0, 0, 0, 0.5)",
                          opacity: 0,
                          transition: "opacity 0.3s ease-in-out",
                        }}
                      >
                        <div className="d-flex flex-column align-items-center">
                          <Skeleton
                            width="70%"
                            height={20}
                            baseColor="#e6ffe6"
                            highlightColor="#c4f7c4"
                          />
                          <div className="d-flex justify-content-center w-100 mt-2">
                            <Skeleton
                              width={60}
                              height={30}
                              baseColor="#e6ffe6"
                              highlightColor="#c4f7c4"
                              className="m-1"
                            />
                            <Skeleton
                              width={60}
                              height={30}
                              baseColor="#e6ffe6"
                              highlightColor="#c4f7c4"
                              className="m-1"
                            />
                          </div>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            ) : (
              <Row>
                {paginatedPopUps.map((popup) => (
                  <Col key={popup.id} md={6} className="mb-3">
                    <PopUpCard
                      popup={popup}
                      handleDeletePopUp={handleDeletePopUp}
                      handleUpdatePopUp={handleUpdatePopUp}
                    />
                  </Col>
                ))}
              </Row>
            )}
            <div className="d-flex justify-content-center mt-1 mb-3">
              <PaginationComponent
                totalPages={totalPages}
                currentPage={currentPage}
                handlePageChange={handlePageChange}
              />
            </div>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default PopUpDashboard;
