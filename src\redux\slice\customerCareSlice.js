// src/redux/customerCareSlice.js
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get the authToken from Redux state
const getAuthToken = (getState) => {
  const { access } = getState()?.customerCare; // Access the 'access' token instead of 'token'
  return access;
};

const getCustomerCareProfileSlug = (getState) => {
  const { slug } = getState()?.customerCare?.customer; // Access the 'access' token instead of 'token'
  // return "testcustomercare-customrcare";
  return slug;
};

// Register a customer
export const registerCustomer = createAsyncThunk(
  "customerCare/registerCustomer",
  async (customerData, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_REGISTER}`,
        customerData
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// Login a customer
export const loginCustomer = createAsyncThunk(
  "customerCare/loginCustomer",
  async (loginData, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_LOGIN}`,
        loginData
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// Logout a customer
export const logoutCustomer = createAsyncThunk(
  "customerCare/logoutCustomer",
  async (_, { getState, rejectWithValue }) => {
    try {
      // Retrieve the access and refresh tokens from the state
      const { access, refresh } = getState().customerCare.customer;

      // If the tokens are missing, reject with an error message
      if (!access || !refresh) {
        return rejectWithValue('Tokens are missing');
      }

      // Prepare the data with the refresh token
      const formData = new FormData();
      formData.append('refresh', refresh);

      // Send the logout request to the API
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_LOGOUT_CUSTOMER}`, // Use appropriate endpoint for customer logout
        formData,
        {
          headers: {
            Authorization: `Bearer ${access}`, // Pass the access token for authentication
          },
        }
      );

      // If the response status is 204, logout is successful
      if (response.status === 204) {
        return { message: "Logged out successfully" };
      } else {
        return rejectWithValue('Logout failed');
      }
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Something went wrong during logout'
      );
    }
  }
);

// Get customer profile
export const getCustomerProfile = createAsyncThunk(
  "customerCare/getCustomerProfile",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // helper function to get the token
      const slug = getCustomerCareProfileSlug(getState); // helper function to get the customer care slug
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_PROFILE}${slug}/`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);


// Get customer profile list
export const getCustomerCareList = createAsyncThunk(
  "customerCare/getCustomerCareList",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Use helper function to get the token

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_PROFILES}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch customer care profiles");
    }
  }
);

// Edit customer profile
export const editCustomerProfile = createAsyncThunk(
  "customerCare/editCustomerProfile",
  async ({ updatedProfile }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Use helper function to get the token
      const slug = getCustomerCareProfileSlug(getState); // helper function to get the customer care slug
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_PROFILE}${slug}`,
        updatedProfile,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// Delete customer profile
export const deleteCustomerProfile = createAsyncThunk(
  "customerCare/deleteCustomerProfile",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Use helper function to get the token
      const slug = getCustomerCareProfileSlug(getState); // helper function to get the customer care slug
      const response = await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_PROFILE}${slug}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// Get customer care questions
export const getCustomerCareQuestions = createAsyncThunk(
  "customerCare/getCustomerCareQuestions",
  async (subjectName, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Use helper function to get the token
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_QUESTIONS}${subjectName}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// Get all customer care questions
export const getAllCustomerCareQuestion = createAsyncThunk(
  "customerCare/getAllCustomerCareQuestion",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Use helper function to get the token
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_ALL_CUSTOMER_CARE_QUESTIONS}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// Patch customer care questions
export const patchCustomerCareQuestions = createAsyncThunk(
  "customerCare/patchCustomerCareQuestions",
  async ({ questionData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Get token
      const isFormData = questionData instanceof FormData; // Check if data is FormData

      const response = await axios.patch(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_QUESTIONS_UPDATE}`,
        questionData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            ...(isFormData ? { "Content-Type": "multipart/form-data" } : {}),
          },
        }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Something went wrong");
    }
  }
);

// Fetch customer dashboard data
export const getCustomerDashboardData = createAsyncThunk(
  "customerCare/getCustomerDashboardData",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState); // Get token

      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CUSTOMER_CARE_DASHBOARD}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch customer dashboard data");
    }
  }
);

// Customer care slice
const customerCareSlice = createSlice({
  name: "customerCare",
  initialState: {
    customerCareQuestions: null,
    customer: null,
    access: null,  // Use 'access' instead of 'token'
    loading: false,
    error: null,
  },
  reducers: {
    setAuthToken: (state, action) => {
      state.access = action.payload; // Store 'access' token
    },
    clearAuthToken: (state) => {
      state.access = null; // Clear 'access' token on logout
      state.customer = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Register customer
      .addCase(registerCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.customer = action.payload;
      })
      .addCase(registerCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Login customer
      .addCase(loginCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.customer = action.payload;
        state.access = action.payload.access; // Store 'access' token
      })
      .addCase(loginCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Logout customer
      .addCase(logoutCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logoutCustomer.fulfilled, (state) => {
        state.loading = false;
        state.customer = null;
        state.access = null; // Clear 'access' token on logout
      })
      .addCase(logoutCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // get customer care profile list

      .addCase(getCustomerCareList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCustomerCareList.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(getCustomerCareList.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get customer profile
      .addCase(getCustomerProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCustomerProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.customer = action.payload;
      })
      .addCase(getCustomerProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Edit customer profile
      .addCase(editCustomerProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(editCustomerProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.customer = action.payload;
      })
      .addCase(editCustomerProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Delete customer profile
      .addCase(deleteCustomerProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteCustomerProfile.fulfilled, (state) => {
        state.loading = false;
        state.customer = null;
        state.access = null;
      })
      .addCase(deleteCustomerProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Handle pending state
      .addCase(getCustomerCareQuestions.pending, (state) => {
        state.loading = true;
        state.error = null; // Reset error when request starts
      })
      // Handle fulfilled state
      .addCase(getCustomerCareQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null; 
      })
      // Handle rejected state
      .addCase(getCustomerCareQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Get all customer care questions
      .addCase(getAllCustomerCareQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllCustomerCareQuestion.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.customerCareQuestions = action.payload;
      })
      .addCase(getAllCustomerCareQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Patch customer care questions
      .addCase(patchCustomerCareQuestions.pending, (state) => {
        state.loading = true;
      })
      .addCase(patchCustomerCareQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null; 
      })
      .addCase(patchCustomerCareQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(getCustomerDashboardData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCustomerDashboardData.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(getCustomerDashboardData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setAuthToken, clearAuthToken } = customerCareSlice.actions;

export default customerCareSlice.reducer;
