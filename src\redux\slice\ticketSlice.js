import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the authToken from Redux state
const getAuthToken = (getState) => {
  const { access } = getState().customerCare; // Adjust state path as needed
  return access;
};

// Create a ticket
export const createTicket = createAsyncThunk(
  'tickets/create',
  async ({ data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      let requestBody;
      let headers = {
        Authorization: `Bearer ${token}`,
      };

      // Check if attachments exist
      if (data.attachments instanceof File) {
        const formData = new FormData();

        Object.keys(data).forEach((key) => {
          if (key === "attachments") {
            formData.append("attachments", data.attachments); // File upload
          } else if (Array.isArray(data[key])) {
            // Properly handle array fields in FormData
            data[key].forEach((value) => {
              formData.append(`${key}`, value);
            });
          } else {
            formData.append(key, data[key]);
          }
        });

        requestBody = formData;
        headers["Content-Type"] = "multipart/form-data";
        console.log("Sending FormData:", [...formData.entries()]); // Debugging
      } else {
        requestBody = data; // Send JSON if no file is present
        headers["Content-Type"] = "application/json";
        console.log("Sending JSON:", data); // Debugging
      }

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}`,
        requestBody,
        { headers }
      );

      return response.data;
    } catch (error) {
      console.error("Error creating question:", error);
      return rejectWithValue(error.response?.data || "Failed to create question");
    }
  }
);

// Get all tickets
export const getAllTickets = createAsyncThunk(
  'tickets/getAll',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch tickets');
    }
  }
);

// Get a specific ticket
export const getTicket = createAsyncThunk(
  'tickets/get',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch ticket');
    }
  }
);

// Update a ticket
export const updateTicket = createAsyncThunk(
  'tickets/update',
  async ({ slug, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}${slug}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update ticket');
    }
  }
);

// Delete a ticket
export const deleteTicket = createAsyncThunk(
  'tickets/delete',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TICKET}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug; // Return the slug to confirm deletion
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete ticket');
    }
  }
);

const ticketSlice = createSlice({
  name: 'tickets',
  initialState: {
    tickets: [],
    ticket: null,
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create ticket
      .addCase(createTicket.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTicket.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(createTicket.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get all tickets
      .addCase(getAllTickets.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllTickets.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(getAllTickets.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get a specific ticket
      .addCase(getTicket.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTicket.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(getTicket.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update a ticket
      .addCase(updateTicket.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTicket.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(updateTicket.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete a ticket
      .addCase(deleteTicket.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteTicket.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(deleteTicket.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default ticketSlice.reducer;
