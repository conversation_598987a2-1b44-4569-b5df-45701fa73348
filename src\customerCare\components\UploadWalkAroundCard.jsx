import React, { useRef } from "react";
import { Card, Button, Form } from "react-bootstrap";

const UploadWalkAroundCard = ({ newWalkAround, setNewWalkAround, handleInputChange, handleFileChange, handleAddWalkAround }) => {
  const fileInputRef = useRef();

  const resetImagePreview = () => {
    if (fileInputRef.current) fileInputRef.current.value = "";
    setNewWalkAround((prev) => ({ ...prev, image: null }));
  };

  return (
    <Card className="mb-3 shadow rounded-3">
      <Card.Body>
        <Card.Title>Add New Walk Around</Card.Title>
        <Form onSubmit={(e) => handleAddWalkAround(e, resetImagePreview)}>
          <Form.Group className="mb-2">
            <Form.Label>Title</Form.Label>
            <Form.Control
              type="text"
              name="title"
              value={newWalkAround.title}
              onChange={handleInputChange}
              placeholder="Enter walk around title"
              required
            />
          </Form.Group>
          <Form.Group className="mb-2">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              name="description"
              value={newWalkAround.description}
              onChange={handleInputChange}
              placeholder="Enter walk around description"
              required
            />
          </Form.Group>
          <Form.Group className="mb-2">
            <Form.Label>Status</Form.Label>
            <Form.Select
              name="status"
              value={newWalkAround.status}
              onChange={handleInputChange}
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </Form.Select>
          </Form.Group>
          <Form.Group className="mb-2">
            <Form.Label>Image</Form.Label>
            <Form.Control
              type="file"
              accept="image/*"
              ref={fileInputRef}
              onChange={handleFileChange}
              required
            />
          </Form.Group>
          <Button type="submit" variant="success" className="w-100 mt-2">
            Add Walk Around
          </Button>
        </Form>
      </Card.Body>
    </Card>
  );
};

export default UploadWalkAroundCard;
