import React, { useEffect, useState } from "react";
import {
  Card,
  Button,
  Row,
  Col,
  Form,
  InputGroup,
  Dropdown,
  Modal,
} from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import Swal from "sweetalert2";
import { useDispatch, useSelector } from "react-redux";
import { getWithdrawalRequests, updateWithdrawalRequest } from "../../redux/slice/rewardSlice";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import NavigationBar from "../../commonComponents/NavigationBar";
import { toast } from "react-hot-toast";

const RewardDashboard = () => {
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.rewards);

  const [withdrawals, setWithdrawals] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [perPage, setPerPage] = useState(5);
  const [currentPage, setCurrentPage] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [modalData, setModalData] = useState({ id: null, transaction_id: "", note: "" });
  const [rejectModalData, setRejectModalData] = useState({ id: null, note: "" });
  const [showRejectModal, setShowRejectModal] = useState(false);

  const fetchWithdrawalRequests = () => {
    dispatch(getWithdrawalRequests())
      .unwrap()
      .then((res) => setWithdrawals(res))
      .catch((err) => toast.error("Failed to fetch withdrawal requests"));
  };

  useEffect(() => {
    fetchWithdrawalRequests();
  }, [dispatch]);

  const filteredData = withdrawals.filter((item) =>
    ["user", "amount", "upi_id", "status", "created_at"].some((key) =>
      item[key]?.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const totalPages = perPage === "All" ? 1 : Math.ceil(filteredData.length / perPage);
  const paginatedData =
    perPage === "All"
      ? filteredData
      : filteredData.slice((currentPage - 1) * perPage, currentPage * perPage);

  const handlePageChange = (page) => setCurrentPage(page);

  const handleApprove = (id) => {
    setModalData({ id, transaction_id: "", note: "" });
    setShowModal(true);
  };

  const handleReject = (id) => {
    setRejectModalData({ id, note: "" });
    setShowRejectModal(true);
  };

  const handleModalSubmit = () => {
    const payload = {
      status: "Approved",
      transaction_id: modalData.transaction_id,
      note: modalData.note,
    };
    dispatch(updateWithdrawalRequest({ id: modalData.id, data: payload }))
      .unwrap()
      .then(() => {
        toast.success("Withdrawal request approved successfully");
        fetchWithdrawalRequests();
      })
      .catch(() => toast.error("Failed to approve withdrawal request"));
    setShowModal(false);
  };

  const handleRejectModalSubmit = () => {
    const payload = {
      status: "Rejected",
      note: rejectModalData.note,
    };
    dispatch(updateWithdrawalRequest({ id: rejectModalData.id, data: payload }))
      .unwrap()
      .then(() => {
        toast.success("Withdrawal request rejected successfully");
        fetchWithdrawalRequests();
      })
      .catch(() => toast.error("Failed to reject withdrawal request"));
    setShowRejectModal(false);
  };

  return (
    <>
    <NavigationBar/>
    <div className="container py-4">
      <Row className="justify-content-center mb-4"> {/* Center-align the search bar and dropdown */}
        <Col xs={12} md={8}>
          <div className="d-flex align-items-center justify-content-center gap-2">
            <InputGroup className="flex-grow-1">
              <Form.Control
                placeholder="Search by any field..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
            <Dropdown>
              <Dropdown.Toggle variant="success" id="dropdown-basic">
                {perPage === "All" ? "All" : `${perPage} per page`}
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {[5, 50, 100, "All"].map((opt, idx) => (
                  <Dropdown.Item key={idx} onClick={() => {
                    setPerPage(opt);
                    setCurrentPage(1);
                  }}>
                    {opt === "All" ? "All" : `${opt} per page`}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            </Dropdown>
          </div>
        </Col>
      </Row>

      <Row>
        {isLoading
          ? Array(5).fill().map((_, idx) => (
              <Col key={idx} md={4} className="mb-3">
                <Card>
                  <Card.Body>
                    <Skeleton height={30} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                    <Skeleton count={3} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                  </Card.Body>
                </Card>
              </Col>
            ))
          : paginatedData.map((item) => (
              <Col key={item.id} md={4} className="mb-3">
                <Card>
                  <Card.Body>
                    <Card.Title>User: {item.user}</Card.Title>
                    <Card.Text>
                      <strong>Amount:</strong> ₹{item.amount}<br />
                      <strong>UPI ID:</strong> {item.upi_id}<br />
                      <strong>Status:</strong> {item.status}<br />
                      <strong>Date:</strong> {new Date(item.created_at).toLocaleString()}
                    </Card.Text>
                    <div className="d-flex justify-content-between">
                      <Button variant="success" onClick={() => handleApprove(item.id)}>Approve</Button>
                      <Button variant="danger" onClick={() => handleReject(item.id)}>Reject</Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))}
      </Row>

      {totalPages > 1 && perPage !== "All" && (
        <PaginationComponent
          totalPages={totalPages}
          currentPage={currentPage}
          handlePageChange={handlePageChange}
        />
      )}

      {/* Approve Modal */}
      <Modal centered show={showModal} onHide={() => setShowModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Approve Withdrawal</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Transaction ID</Form.Label>
              <Form.Control
                type="text"
                value={modalData.transaction_id}
                onChange={(e) =>
                  setModalData({ ...modalData, transaction_id: e.target.value })
                }
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Note (optional)</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                value={modalData.note}
                onChange={(e) =>
                  setModalData({ ...modalData, note: e.target.value })
                }
              />
            </Form.Group>
            <Button variant="primary" onClick={handleModalSubmit}>
              Submit
            </Button>
          </Form>
        </Modal.Body>
      </Modal>

      {/* Reject Modal */}
      <Modal centered show={showRejectModal} onHide={() => setShowRejectModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Reject Withdrawal</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Note</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                value={rejectModalData.note}
                onChange={(e) =>
                  setRejectModalData({ ...rejectModalData, note: e.target.value })
                }
              />
            </Form.Group>
            <Button
              variant="danger"
              onClick={handleRejectModalSubmit}
              disabled={!rejectModalData.note.trim()}
            >
              Submit
            </Button>
          </Form>
        </Modal.Body>
      </Modal>
    </div>
    </>
  );
};

export default RewardDashboard;
