import React, { useState } from "react";
import { <PERSON>, Button, Modal, Form } from "react-bootstrap";

const WalkAroundCard = ({ walkaround, handleDeleteWalkAround, handleUpdateWalkAround }) => {
  const [showEdit, setShowEdit] = useState(false);
  const [showView, setShowView] = useState(false);
  const [editData, setEditData] = useState({
    title: walkaround.title,
    description: walkaround.description,
    status: walkaround.status,
    image: null,
  });

  const handleEditChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "image") {
      setEditData((prev) => ({ ...prev, image: files[0] }));
    } else {
      setEditData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleEditSubmit = (e) => {
    e.preventDefault();
    handleUpdateWalkAround(walkaround.id, editData);
    setShowEdit(false);
  };

  return (
    <Card className="shadow rounded-3">
      {walkaround.image_url && (
        <Card.Img variant="top" src={walkaround.image_url} style={{ maxHeight: 180, objectFit: "cover" }} />
      )}
      <Card.Body>
        <Card.Title>{walkaround.title}</Card.Title>
        <Card.Text className="text-muted small mb-2">{walkaround.description}</Card.Text>
        <div className="d-flex justify-content-between align-items-center mb-2">
          <span className={`badge ${walkaround.status === 'active' ? 'bg-success' : 'bg-secondary'}`}>
            {walkaround.status}
          </span>
          <small className="text-muted">By: {walkaround.user_username}</small>
        </div>
        <div className="d-flex justify-content-center">
          <Button variant="outline-primary" className="m-1" size="sm" onClick={() => setShowEdit(true)}>
            Edit
          </Button>
          <Button variant="outline-info" className="m-1" size="sm" onClick={() => setShowView(true)} disabled={!walkaround.image_url}>
            View
          </Button>
          <Button variant="outline-danger" className="m-1" size="sm" onClick={() => handleDeleteWalkAround(walkaround.id)}>
            Delete
          </Button>
        </div>
      </Card.Body>
      {/* Edit Modal */}
      <Modal show={showEdit} onHide={() => setShowEdit(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Walk Around</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleEditSubmit}>
          <Modal.Body>
            <Form.Group className="mb-2">
              <Form.Label>Title</Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={editData.title}
                onChange={handleEditChange}
                placeholder="Enter walk around title"
                required
              />
            </Form.Group>
            <Form.Group className="mb-2">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={editData.description}
                onChange={handleEditChange}
                placeholder="Enter walk around description"
                required
              />
            </Form.Group>
            <Form.Group className="mb-2">
              <Form.Label>Status</Form.Label>
              <Form.Select
                name="status"
                value={editData.status}
                onChange={handleEditChange}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-2">
              <Form.Label>Image (Optional)</Form.Label>
              <Form.Control
                type="file"
                name="image"
                accept="image/*"
                onChange={handleEditChange}
              />
              <Form.Text className="text-muted">
                Leave empty to keep current image
              </Form.Text>
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowEdit(false)}>
              Cancel
            </Button>
            <Button type="submit" variant="success">
              Save Changes
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
      {/* View Image Modal */}
      <Modal show={showView} onHide={() => setShowView(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Walk Around Image</Modal.Title>
        </Modal.Header>
        <Modal.Body className="d-flex justify-content-center align-items-center" style={{ minHeight: 300 }}>
          {walkaround.image_url ? (
            <img
              src={walkaround.image_url}
              alt="Walk Around"
              style={{ maxWidth: "100%", maxHeight: "70vh", height: "auto", width: "auto", display: "block", margin: "0 auto" }}
            />
          ) : (
            <div className="text-muted">No image available.</div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowView(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Card>
  );
};

export default WalkAroundCard;
