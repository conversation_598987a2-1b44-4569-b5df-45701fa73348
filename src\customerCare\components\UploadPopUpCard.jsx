import React, { useRef } from "react";
import { Card, Button, Form } from "react-bootstrap";

const UploadPopUpCard = ({ newPopUp, setNewPopUp, handleInputChange, handleFileChange, handleAddPopUp }) => {
  const fileInputRef = useRef();

  const resetImagePreview = () => {
    if (fileInputRef.current) fileInputRef.current.value = "";
    setNewPopUp((prev) => ({ ...prev, popup_image: null }));
  };

  return (
    <Card className="mb-3 shadow rounded-3">
      <Card.Body>
        <Card.Title>Add New Pop Up</Card.Title>
        <Form onSubmit={(e) => handleAddPopUp(e, resetImagePreview)}>
          <Form.Group className="mb-2">
            <Form.Label>Title</Form.Label>
            <Form.Control
              type="text"
              name="popup_title"
              value={newPopUp.popup_title}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-2">
            <Form.Label>Content</Form.Label>
            <Form.Control
              as="textarea"
              name="popup_content"
              value={newPopUp.popup_content}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
          <Form.Group className="mb-2">
            <Form.Label>Image (optional)</Form.Label>
            <Form.Control
              type="file"
              accept="image/*"
              ref={fileInputRef}
              onChange={handleFileChange}
            />
          </Form.Group>
          <Button type="submit" variant="success" className="w-100 mt-2">
            Add Pop Up
          </Button>
        </Form>
      </Card.Body>
    </Card>
  );
};

export default UploadPopUpCard;
