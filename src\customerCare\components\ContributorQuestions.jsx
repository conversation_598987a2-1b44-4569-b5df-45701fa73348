import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import { Col, Container, Row } from "react-bootstrap";
import NavigationBar from "../../commonComponents/NavigationBar";
import AllSubjectButtons from "./AllSubjectButtons";

const ContributorQuestions = () => {
  const navigate = useNavigate();
  const access = useSelector((state) => state.customerCare.access);

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
    }
  }, [access, navigate]);

  return (
    <>
      <NavigationBar/>
      <Container>
        <Row className="justify-content-center my-4">         
          <Col md={12} xs={12}>
            <AllSubjectButtons/>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default ContributorQuestions;
