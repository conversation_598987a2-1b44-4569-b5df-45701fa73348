import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get auth token
const getAuthToken = (getState) => {
    const { access } = getState().customerCare;
    return access;
};

//  Fetch all registered devices (GET)
export const getDevices = createAsyncThunk(
  "notifications/getDevices",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_DEVICES_ENDPOINT}`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching devices");
    }
  }
);

//  Send notification (POST)
export const sendNotification = createAsyncThunk(
  "notifications/send",
  async (notificationData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SEND_NOTIFICATION_ENDPOINT}`;

      const response = await axios.post(API_URL, notificationData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error sending notification");
    }
  }
);

//  Upload image (POST)
export const uploadImage = createAsyncThunk(
  "notifications/uploadImage",
  async (imageData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_UPLOAD_IMAGE_ENDPOINT}`;

      const formData = new FormData();
      formData.append("image", imageData);

      const response = await axios.post(API_URL, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
        withCredentials: true,
      });

      return response.data; // Assuming the API returns the path in `data.path`
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error uploading image");
    }
  }
);

const notificationSlice = createSlice({
  name: "notifications",
  initialState: {
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch Devices
      .addCase(getDevices.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getDevices.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(getDevices.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Send Notification
      .addCase(sendNotification.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(sendNotification.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(sendNotification.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Upload Image
      .addCase(uploadImage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(uploadImage.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(uploadImage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Reducer
export default notificationSlice.reducer;
