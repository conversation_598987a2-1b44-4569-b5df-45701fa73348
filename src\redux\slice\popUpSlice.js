import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get auth token
const getAuthToken = (getState) => {
  const { access } = getState().customerCare;
  return access;
};

// Base API URL for popups
const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_POPUP_ENDPOINT}`;

const initialState = {
  isLoading: false,
  error: null,
};

export const fetchPopUps = createAsyncThunk(
  "popups/fetchAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(API_URL, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching popups");
    }
  }
);

export const createPopUp = createAsyncThunk(
  "popups/create",
  async (formData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(API_URL, formData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error creating popup");
    }
  }
);

export const updatePopUp = createAsyncThunk(
  "popups/update",
  async ({ id, formData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.patch(`${API_URL}${id}/`, formData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error updating popup");
    }
  }
);

export const deletePopUp = createAsyncThunk(
  "popups/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(`${API_URL}${id}/`, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return { id };
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error deleting popup");
    }
  }
);

const popUpSlice = createSlice({
  name: "popups",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPopUps.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPopUps.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(fetchPopUps.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(createPopUp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPopUp.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createPopUp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(updatePopUp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePopUp.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updatePopUp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deletePopUp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePopUp.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(deletePopUp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = popUpSlice.actions;
export default popUpSlice.reducer;
