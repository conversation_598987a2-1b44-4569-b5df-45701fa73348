import React, { useState } from "react";
import { <PERSON><PERSON>, Card, Container, Row, Col } from "react-bootstrap";

const DailyTicket = () => {
  // Dummy data based on dailyTickets structure
  const dailyTickets = [
    {
      id: 1,
      student: {
        name: "<PERSON>",
        email: "joh<PERSON><PERSON>@example.com",
      },
      problem: "Cannot log in to the system",
      timing: "10:30 AM",
    },
    {
      id: 2,
      student: {
        name: "<PERSON>",
        email: "j<PERSON><PERSON>@example.com",
      },
      problem: "Forgot my password",
      timing: "11:15 AM",
    },
    {
      id: 3,
      student: {
        name: "<PERSON>",
        email: "<EMAIL>",
      },
      problem: "Page not loading on the website",
      timing: "9:00 AM",
    },
    {
      id: 4,
      student: {
        name: "<PERSON>",
        email: "char<PERSON><PERSON><PERSON>@example.com",
      },
      problem: "UI issue on mobile app",
      timing: "1:00 PM",
    },
    {
      id: 5,
      student: {
        name: "<PERSON>",
        email: "david<PERSON>@example.com",
      },
      problem: "Error 404 on page",
      timing: "2:30 PM",
    },
    {
      id: 6,
      student: {
        name: "<PERSON>",
        email: "emily<PERSON><PERSON><PERSON>@example.com",
      },
      problem: "Cannot submit the form",
      timing: "3:15 PM",
    },
    {
      id: 7,
      student: {
        name: "<PERSON>",
        email: "<EMAIL>",
      },
      problem: "Video not playing",
      timing: "4:45 PM",
    },
    {
      id: 8,
      student: {
        name: "Grace <PERSON>",
        email: "<EMAIL>",
      },
      problem: "Payment failed",
      timing: "5:00 PM",
    },
  ];

  const handleResolve = (ticketId) => {
    console.log("question resolved btn clicked");
  };

  return (
    <Container className="my-4">
      <Row className="justify-content-center align-items-center mt-4">
        <Col xs={12} md={8}>
          <h1>Daily Tickets ({dailyTickets.length})</h1>
        </Col>
      </Row>
      <Row className="justify-content-center align-items-center mt-4">
        {dailyTickets.map((ticket) => (
          <Col key={ticket.id} xs={12} lg={8} className="mb-4">
            <Card className="shadow-sm border-2">
              <Card.Body>
                <Card.Title as="h5" className="fw-bold text-dark">
                  {ticket.problem}
                </Card.Title>
                <Card.Subtitle className="mb-2 text-muted">
                  Asked by <strong>{ticket.student.name}</strong> at{" "}
                  {ticket.timing}
                </Card.Subtitle>
                <Card.Text className="text-muted">
                  <small>{ticket.student.email}</small>
                </Card.Text>
                <div className="d-flex justify-content-end">
                  <Button
                    variant="success"
                    size="sm"
                    onClick={() => handleResolve(ticket.id)}
                  >
                    Resolve
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>
    </Container>
  );
};

export default DailyTicket;
