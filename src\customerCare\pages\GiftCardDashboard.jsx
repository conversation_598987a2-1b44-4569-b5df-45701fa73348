import React, { useState, useEffect } from "react";
import { <PERSON>, But<PERSON>, Card, Container, Row, Col, Modal, Dropdown } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import { FaGift } from "react-icons/fa";
import {
  createGiftCard,
  fetchGiftCards,
  updateGiftCard,
  deleteGiftCard,
} from "../../redux/slice/giftCardSlice";
import NavigationBar from "../../commonComponents/NavigationBar";
import PaginationComponent from "../../commonComponents/PaginationComponent";

const GiftCardDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { access } = useSelector((state) => state.customerCare);

  const [formData, setFormData] = useState({
    code: "",
    balance: "",
    pin: "",
    expires_at: "",
    email: "",
  });

  const [editFormData, setEditFormData] = useState({
    code: "",
    balance: "",
    pin: "",
    expires_at: "",
    email: "",
  });

  const [giftCards, setGiftCards] = useState([]);
  const [editingGiftCard, setEditingGiftCard] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showEditModal, setShowEditModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);

  const [itemsPerPage, setItemsPerPage] = useState(6);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchGiftCardData = async () => {
    try {
      const response = await dispatch(fetchGiftCards()).unwrap();
      setGiftCards(response);
    } catch (err) {
      toast.error("Failed to fetch gift cards.");
    }
  };

  useEffect(() => {
    if (!access) {
      toast.error("Please login first");
      navigate("/customer_care_login");
      return;
    }
    const fetchData = async () => {
      setIsFetching(true);
      await fetchGiftCardData();
      setIsFetching(false);
    };
    fetchData();
  }, [access]);

  const filteredGiftCards = giftCards.filter((g) =>
    g.code?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    setTotalPages(Math.ceil(filteredGiftCards.length / itemsPerPage));
  }, [filteredGiftCards, itemsPerPage]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "code" && value.length > 16) {
      toast.error("Code cannot exceed 16 characters.");
      setFormData((prev) => ({
        ...prev,
        [name]: value.slice(0, 16),
      }));
      return;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    const payload = {
      code: formData.code.trim(),
      balance: parseFloat(formData.balance),
      pin: formData.pin.trim(),
      expires_at: formData.expires_at,
      email: formData.email.trim() || null,
    };

    try {
      const result = await dispatch(createGiftCard(payload));
      if (result.meta.requestStatus === "fulfilled") {
        toast.success("Gift card created!");
        fetchGiftCardData();
        setFormData({ code: "", balance: "", pin: "", expires_at: "", email: "" });
      } else {
        toast.error("Failed to create gift card.");
      }
    } catch {
      toast.error("An error occurred.");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (giftCard) => {
    setEditingGiftCard(giftCard);
    setEditFormData({
      code: giftCard.code || "",
      balance: giftCard.balance,
      pin: giftCard.pin,
      expires_at: giftCard.expires_at ? giftCard.expires_at.split("T")[0] : "",
      email: giftCard.email || "",
    });
    setShowEditModal(true);
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();

    const payload = {
      code: editFormData.code.trim(),
      balance: parseFloat(editFormData.balance),
      pin: editFormData.pin.trim(),
      expires_at: editFormData.expires_at,
      email: editFormData.email.trim() || null,
    };

    try {
      const result = await dispatch(updateGiftCard({ id: editingGiftCard.id, formData: payload }));
      if (result.meta.requestStatus === "fulfilled") {
        toast.success("Gift card updated!");
        fetchGiftCardData();
        setShowEditModal(false);
        setEditingGiftCard(null);
      } else {
        toast.error("Failed to update gift card.");
      }
    } catch {
      toast.error("An error occurred.");
    }
  };

  const handleDelete = (id) => {
    Swal.fire({
      title: "Delete Gift Card?",
      text: "This will permanently delete the gift card.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const resultAction = await dispatch(deleteGiftCard(id));
          if (resultAction.meta.requestStatus === "fulfilled") {
            toast.success("Gift card deleted!");
            fetchGiftCardData();
          } else {
            toast.error("Failed to delete gift card.");
          }
        } catch {
          toast.error("An error occurred.");
        }
      }
    });
  };

  const paginatedGiftCards = filteredGiftCards.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <>
      <NavigationBar />
      <Container className="my-5">
        <Row>
          <Col md={4}>
            <Card className="shadow">
              <Card.Body>
                <Card.Title className="text-center text-success mb-4">
                  Add Gift Card
                </Card.Title>
                <Form onSubmit={handleSubmit}>
                  <Form.Group className="mb-3">
                    <Form.Label>Gift Code</Form.Label>
                    <Form.Control
                      type="text"
                      name="code"
                      value={formData.code}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                  <Form.Group className="mb-3">
                    <Form.Label>Balance</Form.Label>
                    <Form.Control
                      type="number"
                      name="balance"
                      value={formData.balance}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                  <Form.Group className="mb-3">
                    <Form.Label>PIN</Form.Label>
                    <Form.Control
                      type="text"
                      name="pin"
                      value={formData.pin}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                  <Form.Group className="mb-3">
                    <Form.Label>Expiry Date</Form.Label>
                    <Form.Control
                      type="date"
                      name="expires_at"
                      value={formData.expires_at}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                  <Form.Group className="mb-3">
                    <Form.Label>Email (Optional)</Form.Label>
                    <Form.Control
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="Enter email (optional)"
                    />
                  </Form.Group>
                  <div className="d-grid">
                    <Button type="submit" variant="success" disabled={loading}>
                      {loading ? "Creating..." : "Create"}
                    </Button>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Col>

          <Col md={8}>
            <Row className="my-4 align-items-center">
              <Col xs={8} sm={9}>
                <Form.Control
                  type="text"
                  placeholder="Search by gift code..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Col>
              <Col xs={4} sm={3}>
                <Dropdown>
                  <Dropdown.Toggle variant="success" className="w-100">
                    {itemsPerPage === filteredGiftCards.length ? "All" : `${itemsPerPage} per page`}
                  </Dropdown.Toggle>
                  <Dropdown.Menu>
                    {[6, 50, 100, filteredGiftCards.length].map((val) => (
                      <Dropdown.Item key={val} onClick={() => setItemsPerPage(val)}>
                        {val === filteredGiftCards.length ? "All" : `${val} per page`}
                      </Dropdown.Item>
                    ))}
                  </Dropdown.Menu>
                </Dropdown>
              </Col>
            </Row>

            <Row>
              {isFetching
                ? Array.from({ length: 4 }).map((_, index) => (
                  <Col md={6} lg={4} key={index} className="mb-4">
                    <Skeleton height={150} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                  </Col>
                ))
                : paginatedGiftCards.map((card) => (
                  <Col md={6} lg={4} key={card.id} className="mb-4">
                    <Card className="shadow">
                      <Card.Body>
                        <Card.Title className="text-center">
                          <FaGift className="text-success me-1 fs-3" />
                          <span> ₹{card.amount} Gift Card</span>
                        </Card.Title>
                        <Card.Text>
                          <strong>Code:</strong> {card.code || "Auto-generated"}
                          <br />
                          <strong>Balance:</strong> ₹{card.balance}
                          <br />
                          <strong>PIN:</strong> {card.pin}
                          <br />
                          <strong>Expires:</strong> {card.expires_at ? card.expires_at.split("T")[0] : "No expiry"}
                        </Card.Text>
                        <div className="d-flex justify-content-center">
                          <Button variant="outline-primary" className="me-2" onClick={() => handleEdit(card)}>
                            Edit
                          </Button>
                          <Button variant="outline-danger" onClick={() => handleDelete(card.id)}>
                            Delete
                          </Button>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
            </Row>
            <PaginationComponent
              totalPages={totalPages}
              currentPage={currentPage}
              handlePageChange={setCurrentPage}
            />
          </Col>
        </Row>
      </Container>

      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-success">Edit Gift Card</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Gift Code</Form.Label>
              <Form.Control
                type="text"
                name="code"
                value={editFormData.code}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Balance</Form.Label>
              <Form.Control
                type="number"
                name="balance"
                value={editFormData.balance}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>PIN</Form.Label>
              <Form.Control
                type="text"
                name="pin"
                value={editFormData.pin}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Expiry Date</Form.Label>
              <Form.Control
                type="date"
                name="expires_at"
                value={editFormData.expires_at}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Email (Optional)</Form.Label>
              <Form.Control
                type="email"
                name="email"
                value={editFormData.email}
                onChange={handleEditChange}
                placeholder="Enter email (optional)"
              />
            </Form.Group>
            <div className="d-grid">
              <Button variant="success" type="submit">
                Update
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default GiftCardDashboard;
