import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper to get token
const getAuthToken = (getState) => {
  const { access } = getState().customerCare;
  return access;
};

// Fetch All Gift Cards
export const fetchGiftCards = createAsyncThunk(
  "giftCards/fetchAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GIFTCARD_ENDPOINT}`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching gift cards");
    }
  }
);

// Create Gift Card
export const createGiftCard = createAsyncThunk(
  "giftCards/create",
  async (formData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GIFTCARD_ENDPOINT}`,
        formData,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error creating gift card");
    }
  }
);

// Update Gift Card
export const updateGiftCard = createAsyncThunk(
  "giftCards/update",
  async ({ id, formData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GIFTCARD_ENDPOINT}${id}/`,
        formData,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error updating gift card");
    }
  }
);

// Delete Gift Card
export const deleteGiftCard = createAsyncThunk(
  "giftCards/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GIFTCARD_ENDPOINT}${id}/`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return { id };
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error deleting gift card");
    }
  }
);

// Gift Card Slice
const giftCardSlice = createSlice({
  name: "giftCards",
  initialState: {
    giftCards: [],
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch
      .addCase(fetchGiftCards.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchGiftCards.fulfilled, (state, action) => {
        state.isLoading = false;
        state.giftCards = action.payload;
      })
      .addCase(fetchGiftCards.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Create
      .addCase(createGiftCard.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createGiftCard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.giftCards.push(action.payload);
      })
      .addCase(createGiftCard.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Update
      .addCase(updateGiftCard.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateGiftCard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.giftCards = state.giftCards.map((giftCard) =>
          giftCard.id === action.payload.id ? action.payload : giftCard
        );
      })
      .addCase(updateGiftCard.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Delete
      .addCase(deleteGiftCard.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteGiftCard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.giftCards = state.giftCards.filter(g => g.id !== action.payload.id);
      })
      .addCase(deleteGiftCard.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default giftCardSlice.reducer;
