import React, { useState, useEffect } from "react";
import { Card, Container, Row, Col } from "react-bootstrap";
import Skeleton from "react-loading-skeleton";
import { Table } from "react-bootstrap";
import data from "../utils/data";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";

const CustomerDashboard = () => {
  const [loading, setLoading] = useState(true); 
  // const [data, setData] = useState({});

  const dispatch = useDispatch();

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
      console.log("Loading state is now:", false);
    }, 2000); 

    return () => clearTimeout(timer);
  }, []); 

  // useEffect(() => {
  //   const fetchCustomerDashboardData = async () => {
  //     setLoading(true);
  //     try {
  //       const res = await dispatch(fetchCustomerDashboardData());
  //       console.log("Customer dashboard data fetched successfully");
  //       toast.success("Customer dashboard data fetched successfully");
  //       setData(res.payload);
  //     } catch (error) {
  //       console.error("Error fetching customer dashboard data:", error);
  //       toast.error("Error fetching customer dashboard data");
  //     }finally {
  //       setLoading(false);
  //     }
  //   };

  //   fetchCustomerDashboardData();
  // }, []);

  return (
    <Container className="my-4">
      <h3 className="text-center my-4"> Pending Tasks </h3>
      <Row>
        <h4>Question Summary</h4>
        {Object.keys(data.questions_summary).map((key) => (
          <Col xs={12} md={3} className="mb-4" key={key}>
            {loading ? (
              <Skeleton
                height={150}
                baseColor="#e6ffe6"
                highlightColor="#c4f7c4"
              />
            ) : (
              <Card
                className="text-center h-100 shadow-sm bg-success text-white align-items-center"
                style={{ height: "150px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>
                    {key === "questions" ? "Normal Question" : key}
                  </Card.Title>
                  <Card.Text className="display-6 fw-bold">
                    {data.questions_summary[key].total.pending > -1
                      ? data.questions_summary[key].total.pending
                      : data.questions_summary[key].total.open}
                  </Card.Text>
                  <Card.Subtitle>Pending</Card.Subtitle>
                </Card.Body>
              </Card>
            )}
          </Col>
        ))}
      </Row>
      <Row>
        <h4>Current Month Data</h4>
        {Object.keys(data.current_month_data).map((key) => (
          <Col xs={12} md={4} lg={3} className="mb-4" key={key}>
            {loading ? (
              <Skeleton
                height={150}
                baseColor="#e6ffe6"
                highlightColor="#c4f7c4"
              />
            ) : (
              <Card
                className="text-center h-100 shadow-sm bg-success text-white"
                style={{ height: "250px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>
                  {key === "questions" ? "Normal Question (pending)" : `${key} (pending)`}
                  </Card.Title>
                  <Table hover className="rounded-5">
    
                    <tbody className="rounded-5">
                      <tr
                        className={
                          (key === "tickets"
                            ? data.current_month_data[key].daily.open
                            : data.current_month_data[key].daily.pending) > 0
                            ? "table-warning"
                            : ""
                        }
                      >
                        <td>Daily</td>
                        <td>
                          {key === "tickets"
                            ? data.current_month_data[key].daily.open
                            : data.current_month_data[key].daily.pending}
                        </td>
                      </tr>
                      <tr
                        className={
                          (key === "tickets"
                            ? data.current_month_data[key].weekly.open
                            : data.current_month_data[key].weekly.pending) > 0
                            ? "table-warning"
                            : ""
                        }
                      >
                        <td>Weekly</td>
                        <td>
                          {key === "tickets"
                            ? data.current_month_data[key].weekly.open
                            : data.current_month_data[key].weekly.pending}
                        </td>
                      </tr>
                      <tr
                        className={
                          (key === "tickets"
                            ? data.current_month_data[key].monthly.open
                            : data.current_month_data[key].monthly.pending) > 0
                            ? "table-warning"
                            : ""
                        }
                      >
                        <td>Monthly</td>
                        <td>
                          {key === "tickets"
                            ? data.current_month_data[key].monthly.open
                            : data.current_month_data[key].monthly.pending}
                        </td>
                      </tr>
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            )}
          </Col>
        ))}
      </Row>
      <Row>
        <h4>Previous Month Data</h4>
        {Object.keys(data.previous_month_data).map((key) => (
          <Col xs={12} md={4} lg={3} className="mb-4" key={key}>
            {loading ? (
              <Skeleton
                height={150}
                baseColor="#e6ffe6"
                highlightColor="#c4f7c4"
              />
            ) : (
              <Card
                className="text-center h-100 shadow-sm bg-success text-white"
                style={{ height: "250px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>
                  {key === "questions" ? "Normal Question (pending)" : `${key} (pending)`}
                  </Card.Title>
                  
                  <Table hover>                   
                    <tbody>
                      <tr
                        className={
                          (key === "tickets"
                            ? data.previous_month_data[key].monthly.open
                            : data.previous_month_data[key].monthly.pending) > 0
                            ? "table-warning"
                            : ""
                        }
                      >
                        <td>Monthly</td>
                        <td>
                          {key === "tickets"
                            ? data.previous_month_data[key].monthly.open
                            : data.previous_month_data[key].monthly.pending}
                        </td>
                      </tr>
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            )}
          </Col>
        ))}
      </Row>
      <Row>
        <h4>Third Month Data</h4>
        {Object.keys(data.third_month_data).map((key) => (
          <Col xs={12} md={4} lg={3} className="mb-4" key={key}>
            {loading ? (
              <Skeleton
                height={150}
                baseColor="#e6ffe6"
                highlightColor="#c4f7c4"
              />
            ) : (
              <Card
                className="text-center h-100 shadow-sm bg-success text-white"
                style={{ height: "250px", borderRadius: "8px" }}
              >
                <Card.Body className="d-flex flex-column justify-content-center">
                  <Card.Title>
                  {key === "questions" ? "Normal Question (pending)" : `${key} (pending)`}
                  </Card.Title>
                  <Table hover>
                   
                    <tbody>
                      <tr
                        className={
                          (key === "tickets"
                            ? data.third_month_data[key].monthly.open
                            : data.third_month_data[key].monthly.pending) > 0
                            ? "table-warning"
                            : ""
                        }
                      >
                        <td>Monthly</td>

                        <td>
                          {key === "tickets"
                            ? data.third_month_data[key].monthly.open
                            : data.third_month_data[key].monthly.pending}
                        </td>
                      </tr>
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            )}
          </Col>
        ))}
      </Row>
    </Container>
  );
};

export default CustomerDashboard;
