import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const RejectQuestionModal = ({ show, onClose, onSubmit, entityType, itemId, previousStatus }) => {
  const [rejectionReason, setRejectionReason] = useState("");
  const [attachment, setAttachment] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Pre-fill rejection reason if question was already rejected
    if (previousStatus?.status === "rejected") {
      setRejectionReason(previousStatus?.reason || "");
    } else {
      setRejectionReason("");
    }
  }, [previousStatus]);

  useEffect(() => {
    let objectUrl;
    if (attachment) {
      objectUrl = URL.createObjectURL(attachment);
    }

    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl); // Cleanup memory leak
      }
    };
  }, [attachment]);

  const handleFileChange = (e) => {
    setAttachment(e.target.files[0]);
  };

  const handleSubmit = () => {
    if (!rejectionReason.trim()) {
      alert("Please provide a rejection reason.");
      return;
    }

    setIsSubmitting(true);
    onSubmit(rejectionReason, attachment, entityType, itemId);  // Pass itemId here
  };

  const getFullAttachmentUrl = (path) => {
    if (!path.startsWith("http")) {
      return `${import.meta.env.VITE_BASE_URL}/${path}`;
    }
    return path;
  };

  return (
    <Modal show={show} onHide={onClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Provide Rejection Reason</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          {/* Rejection Reason Input */}
          <Form.Group className="mb-3" controlId="rejectionReason">
            <Form.Label>Reason for Rejection</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              placeholder="Enter reason for rejection"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
            />
          </Form.Group>

          {/* File Upload Input */}
          <Form.Group controlId="fileUpload" className="mb-3">
            <Form.Label>Attachment (Optional)</Form.Label>
            <Form.Control type="file" onChange={handleFileChange} accept=".jpg,.png,.pdf,.docx" />
          </Form.Group>

          {/* Preview Section */}
          {attachment && (
            <div className="mb-3">
              <Form.Label>Preview:</Form.Label>
              {attachment.type.startsWith("image/") ? (
                <img src={URL.createObjectURL(attachment)} alt="Preview" className="img-fluid" />
              ) : (
                <p>{attachment.name}</p>
              )}
            </div>
          )}

          {/* Existing Attachment Preview */}
          {!attachment && previousStatus?.status === "rejected" && previousStatus?.reason_document && (
            <div className="mb-3">
              {previousStatus.reason_document.match(/\.(jpg|png)$/i) ? (
                <img src={getFullAttachmentUrl(previousStatus.reason_document)} alt="Existing Attachment" className="img-fluid" />
              ) : (
                <a href={getFullAttachmentUrl(previousStatus.reason_document)} target="_blank" rel="noopener noreferrer">
                  View Attachment
                </a>
              )}
            </div>
          )}
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button variant="success" onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};


export default RejectQuestionModal;
