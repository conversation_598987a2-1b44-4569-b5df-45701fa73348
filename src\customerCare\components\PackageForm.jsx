import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Card, Row, Col } from "react-bootstrap";

const PackageForm = ({ onSubmit, setSearchTerm }) => {
  const [formData, setFormData] = useState({
    name: "",
    description_line_01: "",
    description_line_02: "",
    description_line_03: "",
    description_line_04: "",
    description_line_05: "",
    price: "",
    discount_price: "",
    recommended: false,
    duration_months: 12,
  });

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;
    setFormData((prev) => ({ ...prev, [name]: newValue }));

    if (name === "name") {
      setSearchTerm(value);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
    setFormData({
      name: "",
      description_line_01: "",
      description_line_02: "",
      description_line_03: "",
      description_line_04: "",
      description_line_05: "",
      price: "",
      discount_price: "",
      recommended: false,
      duration_months: 12,
    });
  };

  return (
    <Card className="shadow">
      <Card.Body>
        <Card.Title>Create Package</Card.Title>
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Package Name</Form.Label>
            <Form.Control type="text" name="name" value={formData.name} onChange={handleChange} required />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Description 1</Form.Label>
            <Form.Control type="text" name="description_line_01" value={formData.description_line_01} onChange={handleChange} required />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Description 2</Form.Label>
            <Form.Control type="text" name="description_line_02" value={formData.description_line_02} onChange={handleChange} />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Description 3</Form.Label>
            <Form.Control type="text" name="description_line_03" value={formData.description_line_03} onChange={handleChange} />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Description 4</Form.Label>
            <Form.Control type="text" name="description_line_04" value={formData.description_line_04} onChange={handleChange} />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Description 5</Form.Label>
            <Form.Control type="text" name="description_line_05" value={formData.description_line_05} onChange={handleChange} />
          </Form.Group>

          <Row>
            <Col>
              <Form.Group className="mb-3">
                <Form.Label>Price</Form.Label>
                <Form.Control type="number" step="0.01" name="price" value={formData.price} onChange={handleChange} required />
              </Form.Group>
            </Col>
            <Col>
              <Form.Group className="mb-3">
                <Form.Label>Discount Price</Form.Label>
                <Form.Control type="number" step="0.01" name="discount_price" value={formData.discount_price} onChange={handleChange} />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col>
              <Form.Group className="mb-3">
                <Form.Check type="checkbox" label="Recommended" name="recommended" checked={formData.recommended} onChange={handleChange} />
              </Form.Group>
            </Col>
            <Col>
              <Form.Group className="mb-3">
                <Form.Label>Duration (Months)</Form.Label>
                <Form.Control type="number" name="duration_months" value={formData.duration_months} onChange={handleChange} required />
              </Form.Group>
            </Col>
          </Row>

          <Button variant="success" type="submit" className="w-100">Create Package</Button>
        </Form>
      </Card.Body>
    </Card>
  );
};

export default PackageForm;
