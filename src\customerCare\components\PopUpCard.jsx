import React, { useState } from "react";
import { Card, Button, Modal, Form } from "react-bootstrap";

const PopUpCard = ({ popup, handleDeletePopUp, handleUpdatePopUp }) => {
  const [showEdit, setShowEdit] = useState(false);
  const [editData, setEditData] = useState({
    popup_title: popup.popup_title,
    popup_content: popup.popup_content,
    popup_image: null,
  });

  const handleEditChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "popup_image") {
      setEditData((prev) => ({ ...prev, popup_image: files[0] }));
    } else {
      setEditData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleEditSubmit = (e) => {
    e.preventDefault();
    handleUpdatePopUp(popup.id, editData);
    setShowEdit(false);
  };

  return (
    <Card className="shadow rounded-3">
      {popup.popup_image && (
        <Card.Img variant="top" src={popup.popup_image} style={{ maxHeight: 180, objectFit: "cover" }} />
      )}
      <Card.Body>
        <Card.Title>{popup.popup_title}</Card.Title>
        <Card.Text>{popup.popup_content}</Card.Text>
        <div className="d-flex justify-content-between">
          <Button variant="outline-primary" size="sm" onClick={() => setShowEdit(true)}>
            Edit
          </Button>
          <Button variant="outline-danger" size="sm" onClick={() => handleDeletePopUp(popup.id)}>
            Delete
          </Button>
        </div>
      </Card.Body>
      <Modal show={showEdit} onHide={() => setShowEdit(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Pop Up</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleEditSubmit}>
          <Modal.Body>
            <Form.Group className="mb-2">
              <Form.Label>Title</Form.Label>
              <Form.Control
                type="text"
                name="popup_title"
                value={editData.popup_title}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-2">
              <Form.Label>Content</Form.Label>
              <Form.Control
                as="textarea"
                name="popup_content"
                value={editData.popup_content}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-2">
              <Form.Label>Image (optional)</Form.Label>
              <Form.Control
                type="file"
                name="popup_image"
                accept="image/*"
                onChange={handleEditChange}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowEdit(false)}>
              Cancel
            </Button>
            <Button type="submit" variant="success">
              Save Changes
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Card>
  );
};

export default PopUpCard;
