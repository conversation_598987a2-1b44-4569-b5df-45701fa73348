import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";

const SignUpContentModal = ({ show, onClose, content, onSubmit }) => {
  const [formData, setFormData] = useState({ heading: "", subtext_1: "", subtext_2: "", urls: [] });

  useEffect(() => {
    if (content) {
      setFormData(content); // Update formData when content prop changes
    }
  }, [content]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = () => {
    onSubmit(formData);
    onClose();
  };

  return (
    <Modal show={show} onHide={onClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>{content ? "Edit Content" : "Create Content"}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          <Form.Group controlId="formHeading">
            <Form.Label>Heading</Form.Label>
            <Form.Control
              type="text"
              name="heading"
              value={formData.heading}
              onChange={handleChange}
            />
          </Form.Group>
          <Form.Group controlId="formSubtext1">
            <Form.Label>Subtext 1</Form.Label>
            <Form.Control
              type="text"
              name="subtext_1"
              value={formData.subtext_1}
              onChange={handleChange}
            />
          </Form.Group>
          <Form.Group controlId="formSubtext2">
            <Form.Label>Subtext 2</Form.Label>
            <Form.Control
              type="text"
              name="subtext_2"
              value={formData.subtext_2}
              onChange={handleChange}
            />
          </Form.Group>
          <Form.Group controlId="formUrls">
            <Form.Label>URLs</Form.Label>
            <Form.Control
              type="text"
              name="urls"
              value={formData.urls.join(", ")}
              onChange={(e) =>
                setFormData({ ...formData, urls: e.target.value.split(", ") })
              }
            />
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onClose}>
          Close
        </Button>
        <Button variant="primary" onClick={handleSubmit}>
          {content ? "Save Changes" : "Create"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default SignUpContentModal;