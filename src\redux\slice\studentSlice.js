import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get the auth token
const getAuthToken = (getState) => {
  const { access } = getState().customerCare; // Access the 'access' token instead of 'token'
  return access;
};

// Async thunk to fetch the student list
export const getStudentList = createAsyncThunk(
  "students/getStudentList",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      if (!token) {
        throw new Error("Authorization token is missing");
      }

      const response = await axios.get( `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_STUDENT_LIST}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      
      // Return the response data (optional; use it where you dispatch this thunk)
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data || "Failed to fetch the student list"
      );
    }
  }
);

const studentSlice = createSlice({
  name: "students",
  initialState: {
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getStudentList.pending, (state) => {
        state.error = null; // Clear previous errors
      })
      .addCase(getStudentList.fulfilled, (state) => {
        state.error = null; // Clear errors on successful fetch
      })
      .addCase(getStudentList.rejected, (state, action) => {
        state.error = action.payload; // Store errors
      });
  },
});

export default studentSlice.reducer;
