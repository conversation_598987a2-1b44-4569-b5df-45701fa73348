import React from "react";
import { Mo<PERSON>, <PERSON>, Button } from "react-bootstrap";

const MasterQuestionPassageModal = ({
  show,
  handleClose,
  updatedData,
  handleInputChange,
  handleSubmit,
}) => {
  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Question</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group controlId="formTitle">
            <Form.Label>Title</Form.Label>
            <Form.Control
              type="text"
              name="title"
              value={updatedData.title}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
          <Form.Group controlId="formPassageContent" className="mt-3">
            <Form.Label>Passage Content</Form.Label>
            <Form.Control
              as="textarea"
              name="passage_content"
              rows={3}
              value={updatedData.passage_content}
              onChange={handleInputChange}
              required
            />
          </Form.Group>
          <Button variant="success" type="submit" className="mt-3">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default MasterQuestionPassageModal;
