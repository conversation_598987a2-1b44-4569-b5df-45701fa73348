import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get authToken from Redux state
const getAuthToken = (getState) => {
  const { access } = getState().customerCare; 
  return access;
};

// Get all student error logs
export const getStudentsErrors = createAsyncThunk(
  "studentErrorLog/getAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_STUDENT_ERRORS}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to fetch student errors");
    }
  }
);

// Delete a student error log by ID
export const deleteStudentError = createAsyncThunk(
  "studentErrorLog/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_DELETE_STUDENT_ERROR}${id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Failed to delete student error");
    }
  }
);

// Slice
const studentErrorLogSlice = createSlice({
  name: "studentErrorLog",
  initialState: {
    studentErrors: [],
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get all student errors
      .addCase(getStudentsErrors.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStudentsErrors.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(getStudentsErrors.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete student error
      .addCase(deleteStudentError.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteStudentError.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(deleteStudentError.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default studentErrorLogSlice.reducer;
