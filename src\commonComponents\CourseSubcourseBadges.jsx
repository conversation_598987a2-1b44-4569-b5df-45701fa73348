import React, { useState } from "react";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
import { FaGraduationCap, FaChevronDown, FaChevronUp } from "react-icons/fa";

const CourseSubcourseBadges = ({
  courses,
  subcourses,
  language,
  subjects,
  topics,
  subTopics,
  maxVisibleItems = 4
}) => {
  const [showAllCourses, setShowAllCourses] = useState(false);
  const [showAllSubcourses, setShowAllSubcourses] = useState(false);
  const [showAllSubjects, setShowAllSubjects] = useState(false);
  const [showAllTopics, setShowAllTopics] = useState(false);
  const [showAllSubTopics, setShowAllSubTopics] = useState(false);

  const renderBadgeList = (items, label, bgColor, showAll, setShowAll, icon = null) => {
    if (!items || items.length === 0) return null;

    const visibleItems = showAll ? items : items.slice(0, maxVisibleItems);
    const hasMore = items.length > maxVisibleItems;

    return (
      <div className="mb-2">
        <div className="d-flex flex-wrap gap-1 align-items-center">
          {/* Label Badge */}
          <Badge bg={bgColor} size="sm" className="me-1">
            {icon && <span className="me-1">{icon}</span>}
            {label}:
          </Badge>
          
          {/* Individual Item Badges */}
          {visibleItems.map((item, index) => (
            <Badge 
              key={index} 
              bg={bgColor} 
              variant="outline"
              size="sm" 
              className="text-wrap"
              style={{
                backgroundColor: `var(--bs-${bgColor})`,
                color: 'white',
                border: `1px solid var(--bs-${bgColor})`,
                opacity: 0.8,
                fontSize: '0.75rem',
                maxWidth: '200px',
                wordBreak: 'break-word'
              }}
            >
              {item}
            </Badge>
          ))}
          
          {/* Show More/Less Button */}
          {hasMore && (
            <Button
              variant="link"
              size="sm"
              className="p-0 text-decoration-none"
              style={{ fontSize: '0.75rem' }}
              onClick={() => setShowAll(!showAll)}
            >
              {showAll ? (
                <>
                  <FaChevronUp className="me-1" />
                  Show Less
                </>
              ) : (
                <>
                  <FaChevronDown className="me-1" />
                  +{items.length - maxVisibleItems} more
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="course-subcourse-badges">
      {/* Subjects */}
      {renderBadgeList(
        subjects,
        "Subject",
        "primary",
        showAllSubjects,
        setShowAllSubjects
      )}

      {/* Topics */}
      {renderBadgeList(
        topics,
        "Topic",
        "info",
        showAllTopics,
        setShowAllTopics
      )}

      {/* Sub-topics */}
      {renderBadgeList(
        subTopics,
        "Sub-topic",
        "secondary",
        showAllSubTopics,
        setShowAllSubTopics
      )}

      {/* Courses */}
      {renderBadgeList(
        courses,
        "Course",
        "success",
        showAllCourses,
        setShowAllCourses,
        <FaGraduationCap />
      )}

      {/* Sub-courses */}
      {renderBadgeList(
        subcourses,
        "Sub-course",
        "dark",
        showAllSubcourses,
        setShowAllSubcourses
      )}

      {/* Language */}
      {language && (
        <div className="mb-2">
          <div className="d-flex flex-wrap gap-1 align-items-center">
            <Badge bg="light" text="dark" size="sm">
              Language: {language}
            </Badge>
          </div>
        </div>
      )}
    </div>
  );
};

export default CourseSubcourseBadges;
