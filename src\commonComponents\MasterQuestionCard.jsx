import { <PERSON>, But<PERSON> } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import {
  FaEdit,
  FaPlus,
  FaTrashAlt,
  FaQuestionCircle,
  FaRegCircle,
  FaInfoCircle,
} from "react-icons/fa";
import {
  IoEllipsisHorizontalCircleSharp,
  IoFilterCircle,
} from "react-icons/io5";
import { useState } from "react";
import QuestionRejectReasonModal from "./QuestionRejectReasonModal";
import MathTextRenderer from "./MathTextRenderer";

const QuestionCard = ({
  question,
  handleEditQuestion,
  handleDeleteQuestion,
  handleEditOption,
  handleDeleteOption,
  handleShowReason
}) => {
  return (
    <Card
      key={question.slug}
      className="shadow-sm position-relative"
      style={{
        marginBottom: "1rem",
        backgroundColor:
          question?.approval_status === "approved"
            ? "#e6ffee"
            : question.approval_status === "rejected"
            ? "#ffe6e6"
            : "#ffffff",
      }}
    >
      <Card.Body>
        <div className="d-flex justify-content-between">
          <div>
            <Card.Title>
              <h6 style={{ fontSize: "0.9rem" }}>
                Subject: {question.subject_name} | Topic: {question.topic_name}{" "}
                | Sub Topic: {question.sub_topic_name}
              </h6>
            </Card.Title>
            <Card.Text
              style={{
                marginRight: "0.7rem",
                textAlign: "justify",
                fontSize: "1.1rem",
              }}
            >
              <FaQuestionCircle /> <MathTextRenderer text={question.content} />
              <Button
                variant="outline-success"
                className="action-buttons m-1"
                onClick={() => handleEditQuestion(question)}
              >
                <FaEdit size={15} />
              </Button>
              <Button
                variant="outline-danger"
                className="action-buttons m-1"
                onClick={() => handleDeleteQuestion(question.slug)}
              >
                <FaTrashAlt size={15} />
              </Button>
            </Card.Text>

            {question.attachments && (
              <Card.Img
                variant="top"
                src={`${import.meta.env.VITE_BASE_URL}/${question.attachments}`}
                className="img-fluid rounded-3 mb-3"
              />
            )}

            {/* Multiple Choice Options */}
            {question.options && question.options.length > 0 ? (
              <div className="mt-1 mx-3">
                <ol style={{ listStyleType: "decimal", padding: "0rem 2rem" }}>
                  {question.options.map((option) => (
                    <li
                      key={option.option_id}
                      style={{
                        color: option.is_correct ? "#198754" : "#dc3545",
                        padding: "2px",
                        borderRadius: "4px",
                        fontSize: "1rem",
                        margin: "5px 0",
                      }}
                    >
                      <FaRegCircle /> <MathTextRenderer text={option.option_text} />
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => handleEditOption(option, question.slug)}
                        className="ms-2"
                      >
                        <FaEdit size={12} />
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() =>
                          handleDeleteOption(option.slug, question.slug)
                        }
                        className="ms-2"
                      >
                        <FaTrashAlt size={12} />
                      </Button>
                    </li>
                  ))}
                </ol>
              </div>
            ) : (
              <p className="text-info my-3">No options available.</p>
            )}
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

const MasterQuestionCard = ({
  masterQuestion,
  handleEdit,
  handleDelete,
  handleEditQuestion,
  handleDeleteQuestion,
  handleEditOption,
  handleDeleteOption,
}) => {
  const [showReasonModal, setShowReasonModal] = useState(false);

  const handleShowReason = () => setShowReasonModal(true);
  const handleCloseReason = () => setShowReasonModal(false);
  return (
    <>
      <Card
        key={masterQuestion.slug}
        className="shadow position-relative"
        style={{
          width: "100%",
          marginBottom: "1rem",
          backgroundColor:
            masterQuestion.approval_status === "approved"
              ? "#e6ffee"
              : masterQuestion.approval_status === "rejected"
              ? "#ffe6e6"
              : "#ffffb3",
        }}
      >
        <Card.Body>
          {/* Master Question Title and Passage */}
          <div className="mb-3">
            <Card.Title>
              <div className="d-flex align-items-center justify-content-between">
                <div className="w-70">
                  <h5 style={{ fontSize: "1.2rem" }}>
                    <IoEllipsisHorizontalCircleSharp className="fs-5" />{" "}
                    {masterQuestion.title}
                  </h5>
                </div>
                <div className="w-30">
                  <Link to={`/master_question/${masterQuestion.slug}`}>
                    <Button
                      variant="outline-success"
                      className="action-buttons m-1"
                    >
                      <FaPlus size={15} />
                    </Button>
                  </Link>
                  <Button
                    variant="outline-primary"
                    className="action-buttons m-2"
                    onClick={() => handleEdit(masterQuestion)}
                  >
                    <FaEdit size={15} />
                  </Button>
                  <Button
                    variant="outline-danger"
                    className="action-buttons m-1"
                    onClick={() => handleDelete(masterQuestion.slug)}
                  >
                    <FaTrashAlt size={15} />
                  </Button>
                </div>
              </div>
            </Card.Title>
            {/* Add rejection reason link */}
            {masterQuestion?.approval_status === "rejected" && (
              <Button
                variant="link"
                className="text-danger p-0 mb-2"
                onClick={handleShowReason}
              >
                <FaInfoCircle className="me-1" />
                See why it rejected
              </Button>
            )}
            <Card.Text
              style={{
                marginRight: "0.7rem",
                textAlign: "justify",
                fontSize: "1.1rem",
              }}
            >
              <IoFilterCircle className="fs-5" />{" "}
              <MathTextRenderer text={masterQuestion.passage_content} />
            </Card.Text>
          </div>

          {/* Questions Under the Passage */}
          <div>
            {masterQuestion?.questions?.length > 0 ? (
              masterQuestion?.questions?.map((question) => (
                <QuestionCard
                  key={question.slug}
                  question={question}
                  handleEditQuestion={handleEditQuestion}
                  handleDeleteQuestion={handleDeleteQuestion}
                  handleEditOption={handleEditOption}
                  handleDeleteOption={handleDeleteOption}
                />
              ))
            ) : (
              <p className="text-muted mt-3">
                No questions available for this passage.
              </p>
            )}
          </div>
        </Card.Body>
      </Card>

      <QuestionRejectReasonModal
        show={showReasonModal}
        onHide={handleCloseReason}
        reason={masterQuestion?.reason}
        reasonDocument={masterQuestion?.reason_document}
      />
    </>
  );
};

export default MasterQuestionCard;
