import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, Row, Col } from 'react-bootstrap';
import { FaClipboard, FaTag, FaFlag, FaCalendarAlt, FaUserAlt } from 'react-icons/fa';
import { useDispatch } from 'react-redux';
import { updateTicket } from '../../redux/slice/ticketSlice';
import { toast } from 'react-hot-toast'; 

const EditTicketModal = ({ show, handleClose, ticket, onEditSuccess }) => {
  const dispatch = useDispatch();
  const [ticketData, setTicketData] = useState({
    subject: '',
    description: '',
    priority: 'low',
    ticket_status: 'open',
    tags: '',
    resolve_summary: '',
    student_id: '',  // Added field
    customer_id: '', // Added field
  });

  // Populate the form fields when ticket is available
  useEffect(() => {
    if (ticket) {
      setTicketData({
        subject: ticket.subject,
        description: ticket.description,
        priority: ticket.priority,
        ticket_status: ticket.ticket_status,
        tags: ticket.tags,
        resolve_summary: ticket.resolve_summary || '',
        student_id: ticket.student ? ticket.student.id : '',  // Ensure student is included
        customer_id: ticket.customer ? ticket.customer.id : '',  // Ensure customer is included
      });
    }
  }, [ticket]);

  // Handle input change for all form fields
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setTicketData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      // Dispatch the updateTicket action with ticketData and slug
      const updatedTicket = await dispatch(updateTicket({ slug: ticket.slug, data: ticketData })).unwrap();
      onEditSuccess(updatedTicket);  // Call onEditSuccess with the updated ticket
      toast.success('Ticket updated successfully!');
      handleClose();  // Close the modal after updating
    } catch (err) {
      console.error('Failed to update ticket', err);
    }
  };

  return (
    <Modal show={show} onHide={handleClose} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>Edit Ticket</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Row className="mb-3">
            <Col md={6}>
              <Form.Group controlId="formSubject">
                <Form.Label><FaClipboard className="text-success" /> Subject</Form.Label>
                <Form.Control
                  type="text"
                  name="subject"
                  value={ticketData.subject}
                  onChange={handleInputChange}
                  placeholder="Enter subject"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group controlId="formPriority">
                <Form.Label><FaFlag className="text-success" /> Priority</Form.Label>
                <Form.Control
                  as="select"
                  name="priority"
                  value={ticketData.priority}
                  onChange={handleInputChange}
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </Form.Control>
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={6}>
              <Form.Group controlId="formTags">
                <Form.Label><FaTag className="text-success" /> Tags</Form.Label>
                <Form.Control
                  type="text"
                  name="tags"
                  value={ticketData.tags}
                  onChange={handleInputChange}
                  placeholder="Enter tags"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group controlId="formTicketStatus">
                <Form.Label><FaFlag className="text-success" /> Status</Form.Label>
                <Form.Control
                  as="select"
                  name="ticket_status"
                  value={ticketData.ticket_status}
                  onChange={handleInputChange}
                >
                  <option value="open">Open</option>
                  <option value="in-progress">In Progress</option>
                  <option value="closed">Closed</option>
                </Form.Control>
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={12}>
              <Form.Group controlId="formDescription">
                <Form.Label><FaClipboard className="text-success" /> Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={ticketData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the issue"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={12}>
              <Form.Group controlId="formResolveSummary">
                <Form.Label><FaClipboard className="text-success" /> Resolve Summary</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="resolve_summary"
                  value={ticketData.resolve_summary}
                  onChange={handleInputChange}
                  placeholder="Provide resolution summary"
                />
              </Form.Group>
            </Col>
          </Row>

          <Button variant="outline-success" type="submit" className="w-100">
            Update Ticket
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default EditTicketModal;
