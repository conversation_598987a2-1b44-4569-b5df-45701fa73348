import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const getAuthToken = (getState) => {
  const { access } = getState().customerCare;
  return access;
};

const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_WALKAROUND_ENDPOINT}`;

const initialState = {
  isLoading: false,
  error: null,
};

export const fetchWalkArounds = createAsyncThunk(
  "walkarounds/fetchAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(API_URL, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching walkarounds");
    }
  }
);

export const createWalkAround = createAsyncThunk(
  "walkarounds/create",
  async (formData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(API_URL, formData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error creating walkaround");
    }
  }
);

export const updateWalkAround = createAsyncThunk(
  "walkarounds/update",
  async ({ id, formData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.patch(`${API_URL}${id}/`, formData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error updating walkaround");
    }
  }
);

export const deleteWalkAround = createAsyncThunk(
  "walkarounds/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(`${API_URL}${id}/`, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });
      return { id };
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error deleting walkaround");
    }
  }
);

const walkAroundSlice = createSlice({
  name: "walkarounds",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWalkArounds.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWalkArounds.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(fetchWalkArounds.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(createWalkAround.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWalkAround.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createWalkAround.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(updateWalkAround.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateWalkAround.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updateWalkAround.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deleteWalkAround.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteWalkAround.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(deleteWalkAround.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = walkAroundSlice.actions;
export default walkAroundSlice.reducer;
