import React from "react";
import { <PERSON>, But<PERSON> } from "react-bootstrap";

const SignUpContentCard = ({ content, onView, onEdit, onDelete }) => {
  return (
    <Card className="mb-3 shadow-sm">
      <Card.Body>
        <Card.Title>{content.heading}</Card.Title>
        <Card.Text>{content.subtext_1}</Card.Text>
        <div className="d-flex justify-content-center">
          <Button variant="outline-primary" onClick={onView} className="m-1">View</Button>
          <Button variant="outline-info" onClick={onEdit} className="m-1">Edit</Button>
          <Button variant="outline-danger" onClick={onDelete} className="m-1">Delete</Button>
        </div>
      </Card.Body>
    </Card>
  );
};

export default SignUpContentCard;
