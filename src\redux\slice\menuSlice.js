import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get auth token
const getAuthToken = (getState) => {
    const { access } = getState().customerCare;
    return access;
};

//  Fetch all menus (GET)
export const fetchMenus = createAsyncThunk(
  "menus/fetchAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MENU_ENDPOINT}`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching menus");
    }
  }
);

//  Create a new menu (POST)
export const createMenu = createAsyncThunk(
  "menu/create",
  async (formData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MENU_ENDPOINT}`;

      const response = await axios.post(API_URL, formData, {
        headers: { Authorization: `Bearer ${token}` },
        withCredentials: true,
      });

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error creating menu");
    }
  }
);

//  Update a menu (PATCH) - Supports updating images if provided
export const updateMenu = createAsyncThunk(
    "menus/update",
    async ({ id, formData }, { getState, rejectWithValue }) => {
      try {
        const token = getAuthToken(getState);
        const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MENU_ENDPOINT}${id}/`;
  
        // Debugging: Log payload before sending
        for (let pair of formData.entries()) {
          console.log(pair[0], pair[1]);
        }
  
        const response = await axios.patch(API_URL, formData, {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        });
  
        return response.data;
      } catch (error) {
        return rejectWithValue(error.response?.data || "Error updating menu");
      }
    }
  );
  

//  Delete a menu (DELETE)
export const deleteMenu = createAsyncThunk(
  "menus/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MENU_ENDPOINT}${id}/`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return { id };
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error deleting menu");
    }
  }
);

const menuSlice = createSlice({
  name: "menus",
  initialState: {
    menus: [],
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Fetch Menus
      .addCase(fetchMenus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMenus.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(fetchMenus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Create Menu
      .addCase(createMenu.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      
      .addCase(createMenu.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })

      .addCase(createMenu.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Update Menu
      .addCase(updateMenu.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateMenu.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(updateMenu.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Delete Menu
      .addCase(deleteMenu.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteMenu.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(deleteMenu.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Reducer
export default menuSlice.reducer;
