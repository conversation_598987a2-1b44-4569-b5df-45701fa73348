import React, { useState } from "react";
import { Card, Button } from "react-bootstrap";
import EditBannerModal from "./EditBannerModal";

const BannerCard = ({ banner, handleDeleteBanner, handleUpdateBanner }) => {
  const [showEditModal, setShowEditModal] = useState(false);

  const imageUrl = banner?.banner_image
    ? banner?.banner_image
    : "https://via.placeholder.com/200"; // Fallback image

  return (
    <>
      <Card className="position-relative rounded-3 shadow mb-3">
        <Card.Img
          variant="top"
          className="img-fluid rounded-3 banner-image"
          src={imageUrl}
          alt={banner?.banner_name || "Banner"}
        />
        <Card.Body
          className="position-absolute top-0 start-0 end-0 bottom-0 d-flex justify-content-center align-items-center p-3 text-white rounded-3"
          style={{
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            opacity: 0,
            transition: "opacity 0.3s ease-in-out",
          }}
        >
          <div className="d-flex flex-column align-items-center">
            <Card.Title>{banner?.banner_name || "Unnamed Banner"}</Card.Title>
            <div className="d-flex justify-content-center w-100">
              <Button
                variant="outline-primary"
                className="m-1"
                onClick={() => setShowEditModal(true)}
              >
                Edit
              </Button>
              <Button
                variant="outline-danger"
                className="m-1"
                onClick={() => banner?.id && handleDeleteBanner(banner?.id)}
              >
                Delete
              </Button>
            </div>
          </div>
        </Card.Body>

        <style>
          {`
            .card:hover .card-body {
              opacity: 1 !important;
            }
            .banner-image {
              height: 200px;
              object-fit: cover;
            }
          `}
        </style>
      </Card>

      {/* Edit Modal */}
      {showEditModal && (
        <EditBannerModal
          show={showEditModal}
          handleClose={() => setShowEditModal(false)}
          banner={banner}
          handleUpdate={handleUpdateBanner}
        />
      )}
    </>
  );
};

export default BannerCard;
