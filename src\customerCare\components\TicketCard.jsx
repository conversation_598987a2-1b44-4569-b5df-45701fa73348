import React, { useState } from 'react';
import { Card, Button, Modal, Row, Col} from 'react-bootstrap';
import Swal from 'sweetalert2';
import { toast } from 'react-hot-toast';
import { useDispatch } from 'react-redux';
import { deleteTicket } from '../../redux/slice/ticketSlice';
import { FaEnvelope, FaPhoneAlt, FaClipboard, FaTag, FaFlag, FaCalendarAlt, FaUserAlt, FaExclamationTriangle } from 'react-icons/fa'; // Importing different React Icons
import { AiOutlineFileAdd } from 'react-icons/ai'; // Additional icon for attachments
import EditTicketModal from './EditTicketModal.jsx'; // Import the separate edit modal component

const TicketCard = ({ ticket, onEditSuccess }) => {
  const dispatch = useDispatch();
  const [showModal, setShowModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  // Handle opening and closing the ticket details modal
  const handleViewClick = () => {
    setShowModal(true);
  };
  const handleCloseModal = () => setShowModal(false);

  // function to delete a ticket
  // const handleDelete = (slug) => {
  //   Swal.fire({
  //     title: 'Are you sure?',
  //     text: 'This ticket will be permanently deleted!',
  //     icon: 'warning',
  //     showCancelButton: true,
  //     confirmButtonText: 'Yes, delete it!',
  //     cancelButtonText: 'Cancel',
  //   }).then(async (result) => {
  //     if (result.isConfirmed) {
  //       try {
  //         await dispatch(deleteTicket(slug)).unwrap();
  //         Swal.fire('Deleted!', 'The ticket has been deleted.', 'success');
  //         onEditSuccess(); // Trigger ticket list refresh after deletion
  //       } catch (err) {
  //         Swal.fire('Error!', 'There was an error deleting the ticket.', 'error');
  //       }
  //     }
  //   });
  // };

  // Handle opening and closing the edit modal
  const handleEdit = () => {
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => setShowEditModal(false);

  // Determine background class based on ticket status
  const getStatusClass = (status) => {
    switch (status) {
      case 'closed':
        return 'bg-primary bg-opacity-10';

      case 'in-progress':
        return 'bg-warning bg-opacity-10'; 

      default:
        return 'bg-danger bg-opacity-10';

    }
  };

  return (
    <>
      {/* Main Card - Showing essential information */}
      <Card className={`mt-3 shadow border-0 rounded p-3 ${getStatusClass(ticket?.ticket_status)}`}>
        <Card.Body>
          <Row className="mb-2">
            <Col>
              <Card.Title>{ticket?.subject}</Card.Title>
              <Card.Subtitle className="mb-2 text-muted">{ticket?.ticket_status}</Card.Subtitle>
            </Col>
          </Row>

          {/* Show only important details */}
          <Row className="mb-3">
            <Col>
              <p><FaUserAlt className="text-success" /> {ticket?.student?.user?.first_name} {ticket?.student?.user?.last_name}</p>
              <p><FaEnvelope className="text-success" /> {ticket?.student?.user?.email}</p>
              <p><FaPhoneAlt className="text-success" /> {ticket?.student?.phone}</p>
              <p><FaClipboard className="text-success" /> {ticket?.description}</p>
            </Col>
          </Row>

          {/* Action Buttons */}
          <Row className="mt-3">
            <Col className='d-flex justify-content-center'>
              <Button variant="outline-info" className="m-1" size="sm" onClick={handleEdit}>
                Edit
              </Button>
              {/* <Button variant="outline-danger" size="sm" onClick={() => handleDelete(ticket?.slug)}>
                Delete
              </Button> */}
              <Button variant="outline-primary" className="m-1" size="sm" onClick={handleViewClick}>
                View More
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Modal for Viewing All Ticket Details */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{ticket?.subject}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {/* Multi-column layout for ticket details */}
          <Row className="mb-3">
            <Col md={6}>
              <p><FaClipboard className="text-success" /> <strong>Description:</strong> {ticket?.description}</p>
              <p><FaFlag className="text-success" /> <strong>Priority:</strong> {ticket?.priority}</p>
              <p><FaTag className="text-success" /> <strong>Tags:</strong> {ticket?.tags}</p>
              <p><FaExclamationTriangle className="text-success" /> <strong>Resolve Summary:</strong> {ticket?.resolve_summary || "No summary provided"}</p>
            </Col>
            <Col md={6}>
              <p><FaFlag className="text-success" /> <strong>Status:</strong> {ticket?.ticket_status}</p>
              <p><FaCalendarAlt className="text-success" /> <strong>Update Date:</strong> {new Date(ticket?.update_date).toLocaleString()}</p>
              <p><FaCalendarAlt className="text-success" /> <strong>Resolved Date:</strong> {ticket?.date_resolved ? new Date(ticket?.date_resolved).toLocaleString() : 'Not resolved yet'}</p>
            </Col>
          </Row>

          {/* Attachments Section */}
          {ticket?.attachments && (
            <div className="mt-3">
              <strong><AiOutlineFileAdd className="text-success" /> Attachments: </strong>
                <div>
                  <img
                    src={`${import.meta.env.VITE_BASE_URL}/${ticket?.attachments}`}
                    alt={`${ticket?.subject}`}
                    style={{ width: '100%', maxHeight: '300px', objectFit: 'cover', marginBottom: '10px' }}
                  />
                </div>
            </div>
          )}

          {/* Customer Details */}
          <Row className="mt-3">
            <Col md={6}>
              <p><FaUserAlt className="text-success" /> <strong>Cust. Profile:</strong> {ticket?.customer?.user?.first_name} {ticket?.customer?.user?.last_name}</p>
              <p><FaEnvelope className="text-success" /> <strong>Cust. Email:</strong> {ticket?.customer?.user?.email}</p>
              <p><FaPhoneAlt className="text-success" /> <strong>Cust. Contact:</strong> {ticket?.customer?.contact}</p>
            </Col>
            <Col md={6}>
              <p><FaFlag className="text-success" /> <strong>Cust. Status:</strong> {ticket?.customer?.account_status}</p>
              <p><FaTag className="text-success" /> <strong>Ticket Assign:</strong> {ticket?.ticket_assign || 'Not assigned'}</p>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Edit Ticket Modal - Rendered separately from TicketCard */}
      <EditTicketModal
        show={showEditModal}
        handleClose={handleCloseEditModal}
        ticket={ticket}
        onEditSuccess={onEditSuccess}
      />
    </>
  );
};

export default TicketCard;
