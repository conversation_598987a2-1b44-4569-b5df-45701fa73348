import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { createPackage, getPackages, editPackage, deletePackage } from "../../redux/slice/packageSlice";
import PackageForm from "../components/PackageForm";
import PackageCard from "../components/PackageCard";
import PackageModal from "../components/PackageModal";
import PackageViewModal from "../components/PackageViewModal"; // Import View Modal
import PaginationComponent from "../../commonComponents/PaginationComponent"; // Import PaginationComponent
import { Container, Row, Col, Form, Dropdown, DropdownButton } from "react-bootstrap"; // Import Dropdown components
import Swal from "sweetalert2";
import NavigationBar from "../../commonComponents/NavigationBar";
import Skeleton from "react-loading-skeleton"; // Import Skeleton
import "react-loading-skeleton/dist/skeleton.css"; // Import Skeleton CSS
import { useNavigate } from "react-router-dom"; // Import useNavigate
import { toast } from "react-hot-toast"; // Import react-hot-toast

const PackageDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate(); // Initialize navigate
  const { packages, loading } = useSelector((state) => state?.packages); // Use ?. for state
  const { access } = useSelector((state) => state?.customerCare); // Check access in customerCare
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [modalType, setModalType] = useState("");
  const [currentPage, setCurrentPage] = useState(1); // Track current page
  const [itemsPerPage, setItemsPerPage] = useState(6); // Default items per page set to 4

  useEffect(() => {
    if (!access) { // If access is not available
      toast.error("Please login first"); // Show error toast
      navigate("/customer_care_login"); // Redirect to login
      return; // Stop further execution
    }
    dispatch?.(getPackages()); // Use ?. for dispatch
  }, [access, dispatch, navigate]);

  const handleCreate = (formData) => {
    dispatch?.(createPackage({ formData })); // Use ?. for dispatch
  };

  const handleEdit = (pkg) => {
    setSelectedPackage(pkg);
    setModalType("edit");
  };

  const handleView = (pkg) => {
    setSelectedPackage(pkg);
    setModalType("view");
  };

  const handleDelete = (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then((result) => {
      if (result?.isConfirmed) { // Use ?. for result
        dispatch?.(deletePackage(id)); // Use ?. for dispatch
      }
    });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to the first page
  };

  const handleEditSubmit = (formData) => {
    if (selectedPackage?.id) { // Ensure selectedPackage has an id
      dispatch?.(editPackage({ packageId: selectedPackage.id, data: formData })); // Dispatch editPackage with packageId and formData
      setModalType(""); // Close the modal after submission
    }
  };

  const paginatedPackages = packages
    ?.filter((pkg) => pkg?.name?.toLowerCase()?.includes(searchTerm?.toLowerCase())) // Use ?. for packages and strings
    ?.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage); // Paginate packages

  return (
    <>
      <NavigationBar />
      <Container className="mt-4">
        <Row>
          <Col md={5} className="mb-3">
            <PackageForm onSubmit={handleCreate} setSearchTerm={setSearchTerm} />
          </Col>
          <Col md={7} className="mt-3 mt-md-0">
            <div className="d-flex align-items-center justify-content-between mb-3"> {/* Flex container for single-line layout */}
              <Form.Control
                type="text"
                placeholder="Search packages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e?.target?.value)}
                className="me-2 flex-grow-1" // Add margin to the right and make it flexible
              />
              <DropdownButton
                id="dropdown-basic-button"
                title={`${itemsPerPage} items per page`} // Update title to include "items per page"
                variant="primary"
                onSelect={(e) => handleItemsPerPageChange(Number(e))}
              >
                {[6, 12, 16, 32, 64, 100].map((value) => (
                  <Dropdown.Item key={value} eventKey={value}>
                  Per page {value} 
                  </Dropdown.Item>
                ))}
              </DropdownButton>
            </div>
            {loading ? (
              <Row>
                {Array.from({ length: itemsPerPage }).map((_, index) => (
                  <Col md={6} key={index} className="mb-3"> {/* Match card layout */}
                    <Skeleton
                      height={150}
                      baseColor="#e6ffe6"
                      highlightColor="#c4f7c4"
                    />
                  </Col>
                ))}
              </Row>
            ) : (
              <Row>
                {paginatedPackages?.map((pkg) => (
                  <Col md={6} key={pkg?.id}> {/* Use ?. for pkg */}
                    <PackageCard
                      pkg={pkg}
                      onView={() => handleView(pkg)}
                      onEdit={() => handleEdit(pkg)}
                      onDelete={() => handleDelete(pkg?.id)} // Use ?. for pkg
                    />
                  </Col>
                ))}
              </Row>
            )}
            <PaginationComponent
              totalPages={Math.ceil(packages?.length / itemsPerPage) || 1}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
            />
          </Col>
        </Row>

        {/* View Package Modal */}
        {modalType === "view" && (
          <PackageViewModal
            show={true}
            handleClose={() => setModalType("")}
            packageData={selectedPackage}
          />
        )}

        {/* Edit Package Modal */}
        {modalType === "edit" && (
          <PackageModal
            pkg={selectedPackage}
            onClose={() => setModalType("")}
            onSubmit={handleEditSubmit} // Pass handleEditSubmit to PackageModal
          />
        )}
      </Container>
    </>
  );
};

export default PackageDashboard;
