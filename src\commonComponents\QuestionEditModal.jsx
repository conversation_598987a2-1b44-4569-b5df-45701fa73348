import { useState, useRef } from "react";
import { Mo<PERSON>, <PERSON>, Button } from "react-bootstrap";
import { Toaster } from "react-hot-toast";

const QuestionEditModal = ({
  show,
  onHide,
  updatedQuestionData,
  handleSubmitQuestion,
  handleQuestionInputChange,
  handleImageChange,
  imagePreview,
  imageSizeText,
  imageError,
}) => {
  const imageInputRef = useRef(null);

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Question</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmitQuestion}>
          <Form.Group controlId="content">
            <Form.Label>Question Content</Form.Label>
            <Form.Control
              as="textarea"
              name="content"
              value={updatedQuestionData.content}
              onChange={handleQuestionInputChange}
              rows={3}
              required
            />
          </Form.Group>

          {imagePreview && (
            <Form.Group controlId="questionImage" className="mt-3">
              {imageSizeText && <p className="text-success">{imageSizeText}</p>}
              {imageError && <p className="text-danger mb-2">{imageError}</p>}
              <Form.Label>Question Image</Form.Label>
              <Form.Control
                ref={imageInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageChange}
              />
            </Form.Group>
          )}

          {imagePreview && (
            <div className="mb-3">
              <img
                src={imagePreview}
                alt="Preview"
                style={{
                  width: "100%",
                  maxHeight: "200px",
                  objectFit: "cover",
                }}
              />
            </div>
          )}

          <Button variant="outline-success" type="submit" className="mt-3 w-100">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
      <Toaster />
    </Modal>
  );
};

export default QuestionEditModal;
