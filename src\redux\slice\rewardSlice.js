import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper to get token
const getAuthToken = (getState) => {
  const { access } = getState().customerCare;
  return access;
};

// GET withdrawal requests
export const getWithdrawalRequests = createAsyncThunk(
  "rewards/getWithdrawals",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_WITHDRAWALS_ENDPOINT}`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching withdrawal requests");
    }
  }
);

// UPDATE withdrawal request
export const updateWithdrawalRequest = createAsyncThunk(
  "rewards/updateWithdrawal",
  async ({ id, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_UPDATE_WITHDRAWAL_ENDPOINT}${id}/update/`,
        data,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error updating withdrawal request");
    }
  }
);

const rewardSlice = createSlice({
  name: "rewards",
  initialState: {
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get Withdrawals
      .addCase(getWithdrawalRequests.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getWithdrawalRequests.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(getWithdrawalRequests.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Update Withdrawal
      .addCase(updateWithdrawalRequest.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateWithdrawalRequest.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(updateWithdrawalRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default rewardSlice.reducer;
