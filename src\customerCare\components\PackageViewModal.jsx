import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, ListGroup } from "react-bootstrap";

const PackageViewModal = ({ show, handleClose, packageData }) => {
  if (!packageData) return null; // Prevent rendering if no data is available

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Package Details</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <h5>{packageData.name}</h5>
        <p><strong>Price:</strong> ${packageData.price}</p>
        <p><strong>Discount Price:</strong> ${packageData.discount_price}</p>
        <p><strong>Recommended:</strong> {packageData.recommended ? "Yes" : "No"}</p>
        <p><strong>Duration (Months):</strong> {packageData.duration_months}</p>

        {/* Only show descriptions that exist */}
        <strong>Descriptions:</strong>
        <ListGroup className="mt-2">
          {packageData.description_line_01 && <ListGroup.Item>{packageData.description_line_01}</ListGroup.Item>}
          {packageData.description_line_02 && <ListGroup.Item>{packageData.description_line_02}</ListGroup.Item>}
          {packageData.description_line_03 && <ListGroup.Item>{packageData.description_line_03}</ListGroup.Item>}
          {packageData.description_line_04 && <ListGroup.Item>{packageData.description_line_04}</ListGroup.Item>}
          {packageData.description_line_05 && <ListGroup.Item>{packageData.description_line_05}</ListGroup.Item>}
        </ListGroup>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>Close</Button>
      </Modal.Footer>
    </Modal>
  );
};

export default PackageViewModal;
