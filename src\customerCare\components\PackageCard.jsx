import React from "react";
import { <PERSON>, But<PERSON> } from "react-bootstrap";

const PackageCard = ({ pkg, onView, onEdit, onDelete }) => {
  return (
    <Card className="mb-3 shadow-sm">
      <Card.Body>
        <Card.Title>{pkg.name}</Card.Title>
        <Card.Text>Price: ${pkg.price}</Card.Text>
        <div className="d-flex flex-wrap justify-content-center align-items-center">
          <Button variant="outline-info" onClick={onView} className="m-1">View</Button>
          <Button variant="outline-success" onClick={onEdit} className="m-1">Edit</Button>
          <Button variant="outline-danger" onClick={onDelete} className="m-1">Delete</Button>
        </div>
      </Card.Body>
    </Card>
  );
};

export default PackageCard;
